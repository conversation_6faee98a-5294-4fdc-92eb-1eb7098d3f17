import Link from 'next/link'

export default function LatestUpdates() {
  const updates = [
    {
      icon: '🐛',
      text: 'Fixed dark mode bugs in discussion text boxes'
    },
    {
      icon: '🎨',
      text: 'Added new themes and fonts'
    },
    {
      icon: '⚡',
      text: 'Card colors now change instantly'
    },
    {
      icon: '🔧',
      text: 'Improved todo list functionality'
    }
  ]

  return (
    <section className="section-padding bg-white">
      <div className="container-custom">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div>
            <div className="inline-flex items-center bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium mb-4">
              <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
              Latest Update
            </div>
            
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Version 5.10 Now Available
            </h2>
            
            <p className="text-xl text-gray-600 mb-6">
              Released December 2024 with bug fixes, new themes, and performance improvements.
            </p>

            <div className="space-y-4 mb-8">
              {updates.map((update, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <span className="text-xl">{update.icon}</span>
                  <span className="text-gray-700">{update.text}</span>
                </div>
              ))}
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <Link href="/changelog" className="btn-secondary">
                View Full Changelog
              </Link>
              <Link href="/download" className="btn-primary">
                Download Update
              </Link>
            </div>
          </div>

          {/* Visual */}
          <div className="relative">
            <div className="bg-gradient-to-br from-primary-50 to-primary-100 rounded-2xl p-8">
              <div className="bg-white rounded-xl shadow-lg p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="font-semibold text-gray-900">BetterCanvas v5.10</h3>
                  <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-medium">
                    Updated
                  </span>
                </div>
                
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span className="text-sm text-gray-600">Dark mode improvements</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                    <span className="text-sm text-gray-600">New theme collection</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                    <span className="text-sm text-gray-600">Performance boost</span>
                  </div>
                </div>

                <div className="mt-6 pt-4 border-t border-gray-200">
                  <div className="flex justify-between text-sm text-gray-500">
                    <span>Release Date</span>
                    <span>Dec 2024</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Floating notification */}
            <div className="absolute -top-4 -right-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-semibold animate-pulse">
              New!
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
