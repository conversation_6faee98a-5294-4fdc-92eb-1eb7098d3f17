<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Thank You - Better Canvas Resources</title>
    <meta name="description" content="Thank you for contacting Better Canvas Resources. We'll get back to you soon!">
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🎨</text></svg>">
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center space-x-2">
                    <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                        <span class="text-white font-bold text-sm">BC</span>
                    </div>
                    <span class="text-xl font-bold text-gray-900">Better Canvas</span>
                </div>

                <!-- Navigation Links -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="index.html" class="text-gray-700 hover:text-primary-600 font-medium transition-colors">Home</a>
                    <a href="download.html" class="text-gray-700 hover:text-primary-600 font-medium transition-colors">Download</a>
                    <a href="features.html" class="text-gray-700 hover:text-primary-600 font-medium transition-colors">Features</a>
                    <a href="contact.html" class="text-gray-700 hover:text-primary-600 font-medium transition-colors">Contact</a>
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button onclick="toggleMobileMenu()" class="text-gray-700 hover:text-primary-600 focus:outline-none">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Mobile menu -->
            <div id="mobile-menu" class="hidden md:hidden pb-4">
                <div class="flex flex-col space-y-2">
                    <a href="index.html" class="text-gray-700 hover:text-primary-600 font-medium py-2 transition-colors">Home</a>
                    <a href="download.html" class="text-gray-700 hover:text-primary-600 font-medium py-2 transition-colors">Download</a>
                    <a href="features.html" class="text-gray-700 hover:text-primary-600 font-medium py-2 transition-colors">Features</a>
                    <a href="contact.html" class="text-gray-700 hover:text-primary-600 font-medium py-2 transition-colors">Contact</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="min-h-screen flex items-center justify-center py-16">
        <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <!-- Success Icon -->
            <div class="w-24 h-24 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-8">
                <svg class="w-12 h-12 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
            </div>

            <!-- Thank You Message -->
            <h1 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                Thank You!
            </h1>
            
            <p class="text-xl text-gray-600 mb-8 leading-relaxed">
                Your message has been sent successfully. We appreciate you reaching out to us about Better Canvas.
            </p>

            <!-- What's Next -->
            <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-8 mb-8">
                <h2 class="text-2xl font-semibold text-gray-900 mb-6">What happens next?</h2>
                
                <div class="grid md:grid-cols-3 gap-6 text-left">
                    <div class="flex items-start">
                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-4 mt-1">
                            <span class="text-blue-600 font-bold text-sm">1</span>
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-900 mb-2">We Review</h3>
                            <p class="text-sm text-gray-600">Our team will review your message and determine the best way to help you.</p>
                        </div>
                    </div>

                    <div class="flex items-start">
                        <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-4 mt-1">
                            <span class="text-green-600 font-bold text-sm">2</span>
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-900 mb-2">We Respond</h3>
                            <p class="text-sm text-gray-600">You'll receive a response within 24 hours during business days.</p>
                        </div>
                    </div>

                    <div class="flex items-start">
                        <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-4 mt-1">
                            <span class="text-purple-600 font-bold text-sm">3</span>
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-900 mb-2">We Help</h3>
                            <p class="text-sm text-gray-600">We'll provide the assistance or information you need.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="index.html" 
                   class="bg-primary-600 hover:bg-primary-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200 inline-flex items-center justify-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                    </svg>
                    Back to Home
                </a>
                
                <a href="faq.html" 
                   class="bg-white hover:bg-gray-50 text-gray-900 font-semibold py-3 px-6 rounded-lg border border-gray-300 transition-colors duration-200 inline-flex items-center justify-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Browse FAQ
                </a>
            </div>

            <!-- Additional Resources -->
            <div class="mt-12 text-center">
                <p class="text-gray-600 mb-4">While you wait, explore these helpful resources:</p>
                <div class="flex flex-wrap justify-center gap-4 text-sm">
                    <a href="tutorials.html" class="text-primary-600 hover:text-primary-700 underline">Installation Guide</a>
                    <a href="features.html" class="text-primary-600 hover:text-primary-700 underline">Feature Overview</a>
                    <a href="themes.html" class="text-primary-600 hover:text-primary-700 underline">Theme Gallery</a>
                    <a href="troubleshooting.html" class="text-primary-600 hover:text-primary-700 underline">Troubleshooting</a>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="flex items-center justify-center space-x-2 mb-4">
                <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                    <span class="text-white font-bold text-sm">BC</span>
                </div>
                <span class="text-xl font-bold">Better Canvas</span>
            </div>
            <p class="text-gray-400 text-sm">
                © 2024 Better Canvas Resources. All rights reserved.
            </p>
        </div>
    </footer>

    <script>
        function toggleMobileMenu() {
            const menu = document.getElementById('mobile-menu');
            menu.classList.toggle('hidden');
        }

        // Auto-redirect after 30 seconds (optional)
        setTimeout(() => {
            const redirectNotice = document.createElement('div');
            redirectNotice.className = 'fixed bottom-4 right-4 bg-blue-600 text-white px-4 py-2 rounded-lg text-sm';
            redirectNotice.innerHTML = 'Redirecting to home page in 10 seconds...';
            document.body.appendChild(redirectNotice);
            
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 10000);
        }, 30000);
    </script>
</body>
</html>
