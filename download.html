<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Download Better Canvas - Free Chrome & Firefox Extension | Better Canvas Hub</title>
    <meta name="description" content="Download Better Canvas extension for Chrome and Firefox. Get dark mode, better canvas themes, GPA calculator and more Canvas enhancements. Free better canvas chrome extension and firefox download.">
    <meta name="keywords" content="better canvas download, better canvas extension, better canvas chrome extension, better canvas firefox, canvas extension download, better canvas themes">

    <!-- Open Graph -->
    <meta property="og:title" content="Download Better Canvas - Free Chrome & Firefox Extension">
    <meta property="og:description" content="Download Better Canvas extension for Chrome and Firefox. Get dark mode, better canvas themes, GPA calculator and more Canvas enhancements.">
    <meta property="og:type" content="website">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                        }
                    }
                }
            }
        }
    </script>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        body { font-family: 'Inter', system-ui, sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
    </style>

    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "SoftwareApplication",
        "name": "Better Canvas",
        "description": "Better Canvas extension that enhances Canvas LMS with dark mode, better canvas themes, and productivity tools",
        "applicationCategory": "BrowserApplication",
        "operatingSystem": "Chrome, Firefox",
        "downloadUrl": "https://chrome.google.com/webstore/detail/better-canvas/cndibmoanboadcifjkjbdpjgfedanolh",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
        }
    }
    </script>
</head>
<body class="antialiased">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="index.html" class="flex items-center space-x-2">
                        <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-sm">BC</span>
                        </div>
                        <span class="text-xl font-bold text-gray-900">Better Canvas</span>
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <nav class="hidden md:flex items-center space-x-8">
                    <a href="index.html" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">Home</a>
                    <a href="download.html" class="text-primary-600 font-medium">Download</a>
                    <a href="features.html" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">Features</a>
                    <a href="tutorials.html" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">Tutorials</a>
                    <a href="themes.html" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">Themes</a>
                    <a href="faq.html" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">FAQ</a>
                </nav>

                <!-- CTA Button -->
                <div class="hidden md:flex">
                    <a href="#download-section" class="bg-primary-600 hover:bg-primary-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200">
                        Download Now
                    </a>
                </div>

                <!-- Mobile menu button -->
                <button class="md:hidden p-2" onclick="toggleMobileMenu()">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>

            <!-- Mobile Navigation -->
            <div id="mobile-menu" class="hidden md:hidden py-4 border-t border-gray-200">
                <nav class="flex flex-col space-y-4">
                    <a href="index.html" class="text-gray-600 hover:text-gray-900 font-medium">Home</a>
                    <a href="download.html" class="text-primary-600 font-medium">Download</a>
                    <a href="features.html" class="text-gray-600 hover:text-gray-900 font-medium">Features</a>
                    <a href="tutorials.html" class="text-gray-600 hover:text-gray-900 font-medium">Tutorials</a>
                    <a href="themes.html" class="text-gray-600 hover:text-gray-900 font-medium">Themes</a>
                    <a href="faq.html" class="text-gray-600 hover:text-gray-900 font-medium">FAQ</a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="py-16 lg:py-24 bg-gradient-to-br from-gray-50 to-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h1 class="text-4xl lg:text-6xl font-bold text-gray-900 leading-tight mb-6">
                    Download Better Canvas Extension
                </h1>
                <p class="text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
                    Get the most popular Canvas enhancement extension. Transform your learning experience with Better Canvas - featuring dark mode,
                    better canvas themes, GPA calculator, and powerful productivity tools.
                </p>

                <!-- Trust Indicators -->
                <div class="flex flex-col sm:flex-row items-center justify-center space-y-2 sm:space-y-0 sm:space-x-8 mb-12">
                    <div class="flex items-center">
                        <div class="flex text-yellow-400 mr-2">
                            <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                            <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                            <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                            <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                            <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                        </div>
                        <span class="text-gray-600 font-medium">4.8/5 rating</span>
                    </div>
                    <div class="text-gray-600">
                        <span class="font-semibold">100,000+</span> downloads
                    </div>
                    <div class="text-gray-600">
                        <span class="font-semibold">100%</span> free
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Download Options Section -->
    <section id="download-section" class="py-16 lg:py-24 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                    Choose Your Browser for Better Canvas
                </h2>
                <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                    Better Canvas extension is available for Chrome and Firefox. Choose your preferred browser to get started.
                </p>
            </div>

            <div class="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
                <!-- Chrome Download -->
                <div class="bg-white rounded-2xl shadow-lg border border-gray-200 p-8 text-center hover:shadow-xl transition-shadow duration-300">
                    <div class="w-20 h-20 mx-auto mb-6 bg-white rounded-2xl flex items-center justify-center shadow-lg">
                        <img src="https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v9/icons/googlechrome.svg" alt="Chrome" class="w-12 h-12">
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">Google Chrome</h3>
                    <p class="text-gray-600 mb-6">Most popular choice with full feature support</p>

                    <!-- Version Info -->
                    <div class="bg-gray-50 rounded-lg p-4 mb-6 text-sm">
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-gray-600">Version:</span>
                            <span class="font-semibold">5.10</span>
                        </div>
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-gray-600">Size:</span>
                            <span class="font-semibold">2.1 MB</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">Updated:</span>
                            <span class="font-semibold">Dec 2024</span>
                        </div>
                    </div>

                    <a href="https://chrome.google.com/webstore/detail/better-canvas/cndibmoanboadcifjkjbdpjgfedanolh"
                       target="_blank"
                       rel="noopener noreferrer"
                       class="w-full bg-primary-600 hover:bg-primary-700 text-white font-semibold py-4 px-6 rounded-lg transition-colors duration-200 inline-flex items-center justify-center text-lg">
                        <img src="https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v9/icons/googlechrome.svg" alt="Chrome" class="w-6 h-6 mr-3 filter brightness-0 invert">
                        Better Canvas Chrome Extension
                    </a>

                    <p class="text-xs text-gray-500 mt-3">
                        Requires Chrome 88 or later
                    </p>
                </div>

                <!-- Firefox Download -->
                <div class="bg-white rounded-2xl shadow-lg border border-gray-200 p-8 text-center hover:shadow-xl transition-shadow duration-300">
                    <div class="w-20 h-20 mx-auto mb-6 bg-white rounded-2xl flex items-center justify-center shadow-lg">
                        <img src="https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v9/icons/firefox.svg" alt="Firefox" class="w-12 h-12">
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">Mozilla Firefox</h3>
                    <p class="text-gray-600 mb-6">Privacy-focused browser with full compatibility</p>

                    <!-- Version Info -->
                    <div class="bg-gray-50 rounded-lg p-4 mb-6 text-sm">
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-gray-600">Version:</span>
                            <span class="font-semibold">5.10</span>
                        </div>
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-gray-600">Size:</span>
                            <span class="font-semibold">2.1 MB</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">Updated:</span>
                            <span class="font-semibold">Dec 2024</span>
                        </div>
                    </div>

                    <a href="https://addons.mozilla.org/en-US/firefox/addon/better-canvas/"
                       target="_blank"
                       rel="noopener noreferrer"
                       class="w-full bg-orange-600 hover:bg-orange-700 text-white font-semibold py-4 px-6 rounded-lg transition-colors duration-200 inline-flex items-center justify-center text-lg">
                        <img src="https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v9/icons/firefox.svg" alt="Firefox" class="w-6 h-6 mr-3 filter brightness-0 invert">
                        Better Canvas Firefox
                    </a>

                    <p class="text-xs text-gray-500 mt-3">
                        Requires Firefox 91 or later
                    </p>
                </div>
            </div>

            <!-- Security Notice -->
            <div class="mt-12 max-w-2xl mx-auto">
                <div class="bg-green-50 border border-green-200 rounded-lg p-6">
                    <div class="flex items-start">
                        <svg class="w-6 h-6 text-green-600 mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        <div>
                            <h4 class="font-semibold text-green-900 mb-2">Safe & Verified Download</h4>
                            <p class="text-green-800 text-sm">
                                Better Canvas extension is verified by browser stores and scanned for malware.
                                No personal data is collected or transmitted. Open source and community trusted.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Installation Guide Section -->
    <section class="py-16 lg:py-24 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                    Better Canvas Extension Installation Guide
                </h2>
                <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                    Follow these simple steps to install Better Canvas extension on your browser. Takes less than 2 minutes!
                </p>
            </div>

            <div class="grid lg:grid-cols-2 gap-12">
                <!-- Chrome Installation -->
                <div class="bg-white rounded-2xl shadow-lg p-8">
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                            <svg class="w-8 h-8 text-blue-600" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0z"/>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900">Chrome Installation</h3>
                    </div>

                    <div class="space-y-6">
                        <!-- Step 1 -->
                        <div class="flex items-start">
                            <div class="w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">1</div>
                            <div class="flex-1">
                                <h4 class="font-semibold text-gray-900 mb-2">Visit Chrome Web Store</h4>
                                <p class="text-gray-600 mb-3">Click the "Add to Chrome" button above or visit the Chrome Web Store directly.</p>
                                <div class="bg-gray-100 rounded-lg p-4 text-center">
                                    <div class="text-gray-500 text-sm">[Screenshot: Chrome Web Store page]</div>
                                </div>
                            </div>
                        </div>

                        <!-- Step 2 -->
                        <div class="flex items-start">
                            <div class="w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">2</div>
                            <div class="flex-1">
                                <h4 class="font-semibold text-gray-900 mb-2">Click "Add to Chrome"</h4>
                                <p class="text-gray-600 mb-3">Chrome will ask for permission to install the extension.</p>
                                <div class="bg-gray-100 rounded-lg p-4 text-center">
                                    <div class="text-gray-500 text-sm">[Screenshot: Chrome permission dialog]</div>
                                </div>
                            </div>
                        </div>

                        <!-- Step 3 -->
                        <div class="flex items-start">
                            <div class="w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">3</div>
                            <div class="flex-1">
                                <h4 class="font-semibold text-gray-900 mb-2">Confirm Installation</h4>
                                <p class="text-gray-600 mb-3">Click "Add extension" to complete the installation.</p>
                                <div class="bg-gray-100 rounded-lg p-4 text-center">
                                    <div class="text-gray-500 text-sm">[Screenshot: Installation confirmation]</div>
                                </div>
                            </div>
                        </div>

                        <!-- Step 4 -->
                        <div class="flex items-start">
                            <div class="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">✓</div>
                            <div class="flex-1">
                                <h4 class="font-semibold text-gray-900 mb-2">Start Using BetterCanvas</h4>
                                <p class="text-gray-600">Navigate to your Canvas site and enjoy the enhanced experience!</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Firefox Installation -->
                <div class="bg-white rounded-2xl shadow-lg p-8">
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mr-4">
                            <svg class="w-8 h-8 text-orange-600" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M23.05 11.03c-.111-.313-.277-.62-.484-.906-.426-.588-.96-1.091-1.572-1.479z"/>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900">Firefox Installation</h3>
                    </div>

                    <div class="space-y-6">
                        <!-- Step 1 -->
                        <div class="flex items-start">
                            <div class="w-8 h-8 bg-orange-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">1</div>
                            <div class="flex-1">
                                <h4 class="font-semibold text-gray-900 mb-2">Visit Firefox Add-ons</h4>
                                <p class="text-gray-600 mb-3">Click the "Add to Firefox" button above or visit Firefox Add-ons directly.</p>
                                <div class="bg-gray-100 rounded-lg p-4 text-center">
                                    <div class="text-gray-500 text-sm">[Screenshot: Firefox Add-ons page]</div>
                                </div>
                            </div>
                        </div>

                        <!-- Step 2 -->
                        <div class="flex items-start">
                            <div class="w-8 h-8 bg-orange-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">2</div>
                            <div class="flex-1">
                                <h4 class="font-semibold text-gray-900 mb-2">Click "Add to Firefox"</h4>
                                <p class="text-gray-600 mb-3">Firefox will show a permission dialog for the extension.</p>
                                <div class="bg-gray-100 rounded-lg p-4 text-center">
                                    <div class="text-gray-500 text-sm">[Screenshot: Firefox permission dialog]</div>
                                </div>
                            </div>
                        </div>

                        <!-- Step 3 -->
                        <div class="flex items-start">
                            <div class="w-8 h-8 bg-orange-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">3</div>
                            <div class="flex-1">
                                <h4 class="font-semibold text-gray-900 mb-2">Allow Installation</h4>
                                <p class="text-gray-600 mb-3">Click "Add" to install the BetterCanvas extension.</p>
                                <div class="bg-gray-100 rounded-lg p-4 text-center">
                                    <div class="text-gray-500 text-sm">[Screenshot: Installation confirmation]</div>
                                </div>
                            </div>
                        </div>

                        <!-- Step 4 -->
                        <div class="flex items-start">
                            <div class="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">✓</div>
                            <div class="flex-1">
                                <h4 class="font-semibold text-gray-900 mb-2">Ready to Go!</h4>
                                <p class="text-gray-600">Open Canvas and experience the enhanced interface immediately.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- System Requirements & FAQ Section -->
    <section class="py-16 lg:py-24 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid lg:grid-cols-2 gap-12">
                <!-- System Requirements -->
                <div>
                    <h2 class="text-3xl font-bold text-gray-900 mb-8">System Requirements</h2>

                    <div class="space-y-6">
                        <!-- Chrome Requirements -->
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                            <div class="flex items-center mb-4">
                                <svg class="w-6 h-6 text-blue-600 mr-3" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0z"/>
                                </svg>
                                <h3 class="text-lg font-semibold text-blue-900">Google Chrome</h3>
                            </div>
                            <ul class="space-y-2 text-blue-800">
                                <li class="flex items-center">
                                    <svg class="w-4 h-4 mr-2 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                    Chrome 88 or later
                                </li>
                                <li class="flex items-center">
                                    <svg class="w-4 h-4 mr-2 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                    Windows, macOS, Linux, Chrome OS
                                </li>
                                <li class="flex items-center">
                                    <svg class="w-4 h-4 mr-2 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                    2 MB free storage space
                                </li>
                            </ul>
                        </div>

                        <!-- Firefox Requirements -->
                        <div class="bg-orange-50 border border-orange-200 rounded-lg p-6">
                            <div class="flex items-center mb-4">
                                <svg class="w-6 h-6 text-orange-600 mr-3" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M23.05 11.03c-.111-.313-.277-.62-.484-.906-.426-.588-.96-1.091-1.572-1.479z"/>
                                </svg>
                                <h3 class="text-lg font-semibold text-orange-900">Mozilla Firefox</h3>
                            </div>
                            <ul class="space-y-2 text-orange-800">
                                <li class="flex items-center">
                                    <svg class="w-4 h-4 mr-2 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                    Firefox 91 or later
                                </li>
                                <li class="flex items-center">
                                    <svg class="w-4 h-4 mr-2 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                    Windows, macOS, Linux
                                </li>
                                <li class="flex items-center">
                                    <svg class="w-4 h-4 mr-2 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                    2 MB free storage space
                                </li>
                            </ul>
                        </div>

                        <!-- Additional Info -->
                        <div class="bg-gray-50 border border-gray-200 rounded-lg p-6">
                            <h4 class="font-semibold text-gray-900 mb-3">Additional Information</h4>
                            <ul class="space-y-2 text-gray-700 text-sm">
                                <li>• Works with any Canvas LMS installation</li>
                                <li>• No internet connection required after installation</li>
                                <li>• Automatic updates through browser store</li>
                                <li>• Compatible with Canvas mobile responsive design</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Installation FAQ -->
                <div>
                    <h2 class="text-3xl font-bold text-gray-900 mb-8">Installation FAQ</h2>

                    <div class="space-y-4">
                        <!-- FAQ 1 -->
                        <div class="bg-gray-50 rounded-lg border border-gray-200">
                            <button class="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-100 transition-colors" onclick="toggleFAQ(1)">
                                <span class="font-semibold text-gray-900">Why do I need to grant permissions?</span>
                                <svg id="faq-icon-1" class="w-5 h-5 text-gray-500 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div id="faq-content-1" class="hidden px-6 pb-4">
                                <p class="text-gray-600 text-sm">BetterCanvas needs permission to access Canvas pages to apply themes and enhancements. We only access Canvas domains and never collect personal data.</p>
                            </div>
                        </div>

                        <!-- FAQ 2 -->
                        <div class="bg-gray-50 rounded-lg border border-gray-200">
                            <button class="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-100 transition-colors" onclick="toggleFAQ(2)">
                                <span class="font-semibold text-gray-900">Will this slow down my browser?</span>
                                <svg id="faq-icon-2" class="w-5 h-5 text-gray-500 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div id="faq-content-2" class="hidden px-6 pb-4">
                                <p class="text-gray-600 text-sm">No, BetterCanvas is lightweight and optimized for performance. It only runs on Canvas pages and uses minimal system resources.</p>
                            </div>
                        </div>

                        <!-- FAQ 3 -->
                        <div class="bg-gray-50 rounded-lg border border-gray-200">
                            <button class="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-100 transition-colors" onclick="toggleFAQ(3)">
                                <span class="font-semibold text-gray-900">Can I use this on my school's Canvas?</span>
                                <svg id="faq-icon-3" class="w-5 h-5 text-gray-500 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div id="faq-content-3" class="hidden px-6 pb-4">
                                <p class="text-gray-600 text-sm">Yes! BetterCanvas works with any Canvas LMS installation, including your school's custom Canvas domain.</p>
                            </div>
                        </div>

                        <!-- FAQ 4 -->
                        <div class="bg-gray-50 rounded-lg border border-gray-200">
                            <button class="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-100 transition-colors" onclick="toggleFAQ(4)">
                                <span class="font-semibold text-gray-900">How do I uninstall if needed?</span>
                                <svg id="faq-icon-4" class="w-5 h-5 text-gray-500 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div id="faq-content-4" class="hidden px-6 pb-4">
                                <p class="text-gray-600 text-sm">Right-click the BetterCanvas icon in your browser toolbar and select "Remove from Chrome/Firefox" or manage it through your browser's extensions page.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Need More Help -->
                    <div class="mt-8 p-6 bg-primary-50 border border-primary-200 rounded-lg">
                        <h4 class="font-semibold text-primary-900 mb-2">Need More Help?</h4>
                        <p class="text-primary-800 text-sm mb-4">
                            Can't find what you're looking for? Check our comprehensive guides or get in touch.
                        </p>
                        <div class="flex flex-col sm:flex-row gap-3">
                            <a href="tutorials.html" class="bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors text-sm">
                                View Tutorials
                            </a>
                            <a href="faq.html" class="bg-white hover:bg-gray-50 text-primary-600 font-medium py-2 px-4 rounded-lg border border-primary-300 transition-colors text-sm">
                                Full FAQ
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid md:grid-cols-4 gap-8">
                <!-- Brand -->
                <div class="md:col-span-1">
                    <div class="flex items-center space-x-2 mb-4">
                        <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-sm">BC</span>
                        </div>
                        <span class="text-xl font-bold">Better Canvas</span>
                    </div>
                    <p class="text-gray-400 text-sm leading-relaxed">
                        Transform your Canvas learning experience with Better Canvas extension - featuring dark mode, better canvas themes, GPA calculator, and productivity tools.
                    </p>
                </div>

                <!-- Quick Links -->
                <div>
                    <h3 class="font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-2 text-sm">
                        <li><a href="index.html" class="text-gray-400 hover:text-white transition-colors">Home</a></li>
                        <li><a href="download.html" class="text-gray-400 hover:text-white transition-colors">Download</a></li>
                        <li><a href="features.html" class="text-gray-400 hover:text-white transition-colors">Features</a></li>
                        <li><a href="themes.html" class="text-gray-400 hover:text-white transition-colors">Themes</a></li>
                    </ul>
                </div>

                <!-- Support -->
                <div>
                    <h3 class="font-semibold mb-4">Support</h3>
                    <ul class="space-y-2 text-sm">
                        <li><a href="tutorials.html" class="text-gray-400 hover:text-white transition-colors">Tutorials</a></li>
                        <li><a href="faq.html" class="text-gray-400 hover:text-white transition-colors">FAQ</a></li>
                        <li><a href="troubleshooting.html" class="text-gray-400 hover:text-white transition-colors">Troubleshooting</a></li>
                        <li><a href="contact.html" class="text-gray-400 hover:text-white transition-colors">Contact</a></li>
                    </ul>
                </div>

                <!-- Official Links -->
                <div>
                    <h3 class="font-semibold mb-4">Official Links</h3>
                    <ul class="space-y-2 text-sm">
                        <li><a href="https://chrome.google.com/webstore/detail/better-canvas/cndibmoanboadcifjkjbdpjgfedanolh" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition-colors">Chrome Store</a></li>
                        <li><a href="https://addons.mozilla.org/en-US/firefox/addon/better-canvas/" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition-colors">Firefox Add-ons</a></li>
                        <li><a href="https://github.com/UseBetterCanvas/bettercanvas" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition-colors">GitHub</a></li>
                        <li><a href="https://diditupe.dev/bettercanvas/" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition-colors">Official Site</a></li>
                    </ul>
                </div>
            </div>

            <!-- Bottom Bar -->
            <div class="border-t border-gray-800 mt-12 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <div class="text-sm text-gray-400 mb-4 md:mb-0">
                        © 2024 Better Canvas Resources. All rights reserved.
                    </div>
                    <div class="flex space-x-6 text-sm">
                        <a href="privacy.html" class="text-gray-400 hover:text-white transition-colors">Privacy Policy</a>
                        <a href="terms.html" class="text-gray-400 hover:text-white transition-colors">Terms of Use</a>
                        <a href="disclaimer.html" class="text-gray-400 hover:text-white transition-colors">Disclaimer</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script>
        function toggleMobileMenu() {
            const menu = document.getElementById('mobile-menu');
            menu.classList.toggle('hidden');
        }

        function toggleFAQ(index) {
            const content = document.getElementById(`faq-content-${index}`);
            const icon = document.getElementById(`faq-icon-${index}`);

            if (content.classList.contains('hidden')) {
                content.classList.remove('hidden');
                icon.style.transform = 'rotate(180deg)';
            } else {
                content.classList.add('hidden');
                icon.style.transform = 'rotate(0deg)';
            }
        }
    </script>
</body>
</html>
