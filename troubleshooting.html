<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Better Canvas Troubleshooting - Fix Common Issues | Better Canvas Extension Help</title>
    <meta name="description" content="Fix Better Canvas extension issues with our comprehensive troubleshooting guide. Solve problems with installation, better canvas themes, dark mode, and browser compatibility.">
    <meta name="keywords" content="better canvas troubleshooting, better canvas not working, better canvas extension problems, better canvas themes not working, canvas dark mode issues, better canvas chrome extension fix, better canvas firefox fix">

    <!-- Open Graph -->
    <meta property="og:title" content="Better Canvas Troubleshooting - Fix Common Issues">
    <meta property="og:description" content="Comprehensive troubleshooting guide for Better Canvas extension. Fix installation, themes, and compatibility issues quickly.">
    <meta property="og:type" content="website">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                        }
                    }
                }
            }
        }
    </script>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        body { font-family: 'Inter', system-ui, sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .troubleshoot-item { transition: all 0.3s ease; }
        .solution-content { max-height: 0; overflow: hidden; transition: max-height 0.3s ease; }
        .solution-content.open { max-height: 1000px; }
    </style>

    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "TechArticle",
        "headline": "Better Canvas Troubleshooting Guide",
        "description": "Comprehensive troubleshooting guide for Better Canvas extension covering installation issues, theme problems, and browser compatibility",
        "about": {
            "@type": "SoftwareApplication",
            "name": "Better Canvas",
            "applicationCategory": "BrowserApplication"
        },
        "mainEntity": [
            {
                "@type": "Question",
                "name": "Better Canvas extension not working",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "Check if extension is enabled, refresh Canvas page, clear browser cache, and verify Canvas domain permissions."
                }
            },
            {
                "@type": "Question",
                "name": "Better canvas themes not showing",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "Ensure extension is installed, check theme settings, verify Canvas page compatibility, and try refreshing the page."
                }
            }
        ]
    }
    </script>
</head>
<body class="antialiased">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="index.html" class="flex items-center space-x-2">
                        <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-sm">BC</span>
                        </div>
                        <span class="text-xl font-bold text-gray-900">Better Canvas</span>
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <nav class="hidden md:flex items-center space-x-8">
                    <a href="index.html" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">Home</a>
                    <a href="download.html" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">Download</a>
                    <a href="features.html" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">Features</a>
                    <a href="tutorials.html" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">Tutorials</a>
                    <a href="themes.html" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">Themes</a>
                    <a href="faq.html" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">FAQ</a>
                </nav>

                <!-- CTA Button -->
                <div class="hidden md:flex">
                    <a href="download.html" class="bg-primary-600 hover:bg-primary-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200">
                        Download Now
                    </a>
                </div>

                <!-- Mobile menu button -->
                <button class="md:hidden p-2" onclick="toggleMobileMenu()">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>

            <!-- Mobile Navigation -->
            <div id="mobile-menu" class="hidden md:hidden py-4 border-t border-gray-200">
                <nav class="flex flex-col space-y-4">
                    <a href="index.html" class="text-gray-600 hover:text-gray-900 font-medium">Home</a>
                    <a href="download.html" class="text-gray-600 hover:text-gray-900 font-medium">Download</a>
                    <a href="features.html" class="text-gray-600 hover:text-gray-900 font-medium">Features</a>
                    <a href="tutorials.html" class="text-gray-600 hover:text-gray-900 font-medium">Tutorials</a>
                    <a href="themes.html" class="text-gray-600 hover:text-gray-900 font-medium">Themes</a>
                    <a href="faq.html" class="text-gray-600 hover:text-gray-900 font-medium">FAQ</a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="py-16 lg:py-24 bg-gradient-to-br from-red-50 to-orange-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h1 class="text-4xl lg:text-6xl font-bold text-gray-900 leading-tight mb-6">
                    Better Canvas Troubleshooting
                </h1>
                <p class="text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
                    Having issues with Better Canvas extension? Find quick solutions to common problems including installation issues,
                    better canvas themes not working, browser compatibility, and more.
                </p>

                <!-- Quick Stats -->
                <div class="flex flex-col sm:flex-row items-center justify-center space-y-2 sm:space-y-0 sm:space-x-8 mb-12">
                    <div class="flex items-center">
                        <span class="text-3xl font-bold text-red-600 mr-2">95%</span>
                        <span class="text-gray-600 font-medium">Issues Resolved</span>
                    </div>
                    <div class="text-gray-600">
                        <span class="font-semibold">Step-by-Step</span> Solutions
                    </div>
                    <div class="text-gray-600">
                        <span class="font-semibold">Expert</span> Support
                    </div>
                </div>

                <!-- Emergency Help -->
                <div class="bg-red-100 border border-red-200 rounded-lg p-6 max-w-2xl mx-auto mb-8">
                    <h3 class="font-semibold text-red-900 mb-2">🚨 Need Immediate Help?</h3>
                    <p class="text-red-800 text-sm mb-4">
                        If Better Canvas extension is completely broken, try these quick fixes first.
                    </p>
                    <div class="flex flex-col sm:flex-row gap-3 justify-center">
                        <a href="#quick-fixes" class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors text-sm">
                            Quick Fixes
                        </a>
                        <a href="contact.html" class="bg-white hover:bg-gray-50 text-red-600 font-medium py-2 px-4 rounded-lg border border-red-300 transition-colors text-sm">
                            Contact Support
                        </a>
                    </div>
                </div>

                <!-- Problem Categories -->
                <div class="flex flex-wrap justify-center gap-3">
                    <a href="#installation" class="bg-white hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 rounded-lg border border-gray-300 transition-colors text-sm">
                        🔧 Installation Issues
                    </a>
                    <a href="#themes" class="bg-white hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 rounded-lg border border-gray-300 transition-colors text-sm">
                        🎨 Theme Problems
                    </a>
                    <a href="#features" class="bg-white hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 rounded-lg border border-gray-300 transition-colors text-sm">
                        ⚙️ Feature Issues
                    </a>
                    <a href="#compatibility" class="bg-white hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 rounded-lg border border-gray-300 transition-colors text-sm">
                        🌐 Browser Issues
                    </a>
                    <a href="#performance" class="bg-white hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 rounded-lg border border-gray-300 transition-colors text-sm">
                        ⚡ Performance
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Quick Fixes Section -->
    <section id="quick-fixes" class="py-16 lg:py-24 bg-white">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                    Quick Fixes - Try These First
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Before diving into detailed troubleshooting, try these common solutions that fix 80% of Better Canvas extension issues.
                </p>
            </div>

            <div class="grid md:grid-cols-2 gap-6">
                <!-- Quick Fix 1 -->
                <div class="bg-green-50 border border-green-200 rounded-lg p-6">
                    <div class="flex items-start">
                        <div class="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center mr-4 mt-1">
                            <span class="text-white font-bold text-sm">1</span>
                        </div>
                        <div>
                            <h3 class="font-semibold text-green-900 mb-2">Refresh Canvas Page</h3>
                            <p class="text-green-800 text-sm mb-3">
                                Most issues are resolved by simply refreshing your Canvas page.
                            </p>
                            <div class="bg-green-100 rounded p-3 text-sm text-green-800">
                                <strong>How:</strong> Press Ctrl+F5 (Windows) or Cmd+Shift+R (Mac) for a hard refresh
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Fix 2 -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                    <div class="flex items-start">
                        <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center mr-4 mt-1">
                            <span class="text-white font-bold text-sm">2</span>
                        </div>
                        <div>
                            <h3 class="font-semibold text-blue-900 mb-2">Check Extension Status</h3>
                            <p class="text-blue-800 text-sm mb-3">
                                Verify that Better Canvas extension is enabled in your browser.
                            </p>
                            <div class="bg-blue-100 rounded p-3 text-sm text-blue-800">
                                <strong>How:</strong> Click browser menu → Extensions → Ensure Better Canvas is ON
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Fix 3 -->
                <div class="bg-orange-50 border border-orange-200 rounded-lg p-6">
                    <div class="flex items-start">
                        <div class="w-8 h-8 bg-orange-600 rounded-full flex items-center justify-center mr-4 mt-1">
                            <span class="text-white font-bold text-sm">3</span>
                        </div>
                        <div>
                            <h3 class="font-semibold text-orange-900 mb-2">Clear Browser Cache</h3>
                            <p class="text-orange-800 text-sm mb-3">
                                Clear cached data that might be causing conflicts.
                            </p>
                            <div class="bg-orange-100 rounded p-3 text-sm text-orange-800">
                                <strong>How:</strong> Browser Settings → Privacy → Clear browsing data → Cached files
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Fix 4 -->
                <div class="bg-purple-50 border border-purple-200 rounded-lg p-6">
                    <div class="flex items-start">
                        <div class="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center mr-4 mt-1">
                            <span class="text-white font-bold text-sm">4</span>
                        </div>
                        <div>
                            <h3 class="font-semibold text-purple-900 mb-2">Disable Other Extensions</h3>
                            <p class="text-purple-800 text-sm mb-3">
                                Temporarily disable other Canvas-related extensions.
                            </p>
                            <div class="bg-purple-100 rounded p-3 text-sm text-purple-800">
                                <strong>How:</strong> Extensions menu → Turn off other Canvas extensions → Test Better Canvas
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Still Having Issues -->
            <div class="mt-12 text-center">
                <div class="bg-gray-50 border border-gray-200 rounded-lg p-6">
                    <h4 class="font-semibold text-gray-900 mb-2">Still Having Issues?</h4>
                    <p class="text-gray-600 text-sm mb-4">
                        If the quick fixes didn't work, continue to the detailed troubleshooting sections below.
                    </p>
                    <a href="#installation" class="bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors text-sm">
                        Continue Troubleshooting
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Installation Issues -->
    <section id="installation" class="py-16 lg:py-24 bg-gray-50">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                    Installation Issues
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Solve problems with installing Better Canvas extension on Chrome and Firefox browsers.
                </p>
            </div>

            <div class="space-y-6">
                <!-- Issue 1: Extension Won't Install -->
                <div class="troubleshoot-item bg-white rounded-lg border border-gray-200 shadow-sm">
                    <button class="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors" onclick="toggleSolution(this)">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center mr-4">
                                <span class="text-red-600 font-bold text-sm">!</span>
                            </div>
                            <span class="text-lg font-semibold text-gray-900">Better Canvas extension won't install</span>
                        </div>
                        <svg class="toggle-icon w-5 h-5 text-gray-500 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div class="solution-content px-6 pb-4">
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h4 class="font-semibold text-gray-900 mb-3">Possible Causes & Solutions:</h4>
                            <div class="space-y-3">
                                <div class="flex items-start">
                                    <div class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3"></div>
                                    <div>
                                        <strong class="text-gray-900">Browser compatibility:</strong>
                                        <p class="text-gray-600 text-sm">Ensure you're using Chrome 88+ or Firefox 78+</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <div class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3"></div>
                                    <div>
                                        <strong class="text-gray-900">Network issues:</strong>
                                        <p class="text-gray-600 text-sm">Check internet connection and try again</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <div class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3"></div>
                                    <div>
                                        <strong class="text-gray-900">Browser restrictions:</strong>
                                        <p class="text-gray-600 text-sm">Check if your organization blocks extension installations</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <div class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3"></div>
                                    <div>
                                        <strong class="text-gray-900">Store issues:</strong>
                                        <p class="text-gray-600 text-sm">Try installing from the alternative browser store</p>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-4 p-3 bg-blue-50 rounded border border-blue-200">
                                <p class="text-blue-800 text-sm">
                                    <strong>Step-by-step fix:</strong> 1) Update your browser 2) Clear browser cache 3) Disable VPN/proxy 4) Try incognito mode 5) Contact IT if in organization
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Issue 2: Extension Installed but Not Working -->
                <div class="troubleshoot-item bg-white rounded-lg border border-gray-200 shadow-sm">
                    <button class="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors" onclick="toggleSolution(this)">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center mr-4">
                                <span class="text-yellow-600 font-bold text-sm">?</span>
                            </div>
                            <span class="text-lg font-semibold text-gray-900">Extension installed but not working on Canvas</span>
                        </div>
                        <svg class="toggle-icon w-5 h-5 text-gray-500 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div class="solution-content px-6 pb-4">
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h4 class="font-semibold text-gray-900 mb-3">Check These Settings:</h4>
                            <div class="space-y-3">
                                <div class="flex items-start">
                                    <div class="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3"></div>
                                    <div>
                                        <strong class="text-gray-900">Site permissions:</strong>
                                        <p class="text-gray-600 text-sm">Ensure extension has permission to run on Canvas domains</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <div class="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3"></div>
                                    <div>
                                        <strong class="text-gray-900">Extension enabled:</strong>
                                        <p class="text-gray-600 text-sm">Check that Better Canvas is enabled in extensions menu</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <div class="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3"></div>
                                    <div>
                                        <strong class="text-gray-900">Canvas URL:</strong>
                                        <p class="text-gray-600 text-sm">Verify you're on a supported Canvas LMS domain</p>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-4 p-3 bg-green-50 rounded border border-green-200">
                                <p class="text-green-800 text-sm">
                                    <strong>Quick test:</strong> Right-click on Canvas page → Inspect → Console tab → Look for Better Canvas messages
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Issue 3: Extension Disappeared -->
                <div class="troubleshoot-item bg-white rounded-lg border border-gray-200 shadow-sm">
                    <button class="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors" onclick="toggleSolution(this)">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center mr-4">
                                <span class="text-orange-600 font-bold text-sm">⚠</span>
                            </div>
                            <span class="text-lg font-semibold text-gray-900">Better Canvas extension disappeared after browser update</span>
                        </div>
                        <svg class="toggle-icon w-5 h-5 text-gray-500 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div class="solution-content px-6 pb-4">
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h4 class="font-semibold text-gray-900 mb-3">Recovery Steps:</h4>
                            <ol class="list-decimal list-inside space-y-2 text-gray-600 text-sm">
                                <li>Check if extension is still installed but disabled</li>
                                <li>Look in browser's extension management page</li>
                                <li>Re-enable if found, or reinstall if missing</li>
                                <li>Check for extension updates in store</li>
                                <li>Restore settings from backup if available</li>
                            </ol>
                            <div class="mt-4 p-3 bg-orange-50 rounded border border-orange-200">
                                <p class="text-orange-800 text-sm">
                                    <strong>Prevention tip:</strong> Enable automatic extension updates to avoid compatibility issues with browser updates
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Theme Issues -->
    <section id="themes" class="py-16 lg:py-24 bg-white">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                    Better Canvas Themes Issues
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Solve problems with better canvas themes not working, loading, or displaying correctly.
                </p>
            </div>

            <div class="space-y-6">
                <!-- Theme Issue 1: Themes Not Showing -->
                <div class="troubleshoot-item bg-white rounded-lg border border-gray-200 shadow-sm">
                    <button class="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors" onclick="toggleSolution(this)">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center mr-4">
                                <span class="text-purple-600 font-bold text-sm">🎨</span>
                            </div>
                            <span class="text-lg font-semibold text-gray-900">Better canvas themes not showing or loading</span>
                        </div>
                        <svg class="toggle-icon w-5 h-5 text-gray-500 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div class="solution-content px-6 pb-4">
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h4 class="font-semibold text-gray-900 mb-3">Common Causes & Solutions:</h4>
                            <div class="space-y-3">
                                <div class="flex items-start">
                                    <div class="w-2 h-2 bg-purple-500 rounded-full mt-2 mr-3"></div>
                                    <div>
                                        <strong class="text-gray-900">Extension not loaded:</strong>
                                        <p class="text-gray-600 text-sm">Refresh the Canvas page and wait for extension to load</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <div class="w-2 h-2 bg-purple-500 rounded-full mt-2 mr-3"></div>
                                    <div>
                                        <strong class="text-gray-900">Theme settings disabled:</strong>
                                        <p class="text-gray-600 text-sm">Check Better Canvas settings and enable theme functionality</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <div class="w-2 h-2 bg-purple-500 rounded-full mt-2 mr-3"></div>
                                    <div>
                                        <strong class="text-gray-900">Canvas page compatibility:</strong>
                                        <p class="text-gray-600 text-sm">Some Canvas pages may not support themes - try dashboard or courses page</p>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-4 p-3 bg-purple-50 rounded border border-purple-200">
                                <p class="text-purple-800 text-sm">
                                    <strong>Quick fix:</strong> Click Better Canvas icon → Settings → Themes → Ensure "Enable Themes" is checked
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Theme Issue 2: Dark Mode Not Working -->
                <div class="troubleshoot-item bg-white rounded-lg border border-gray-200 shadow-sm">
                    <button class="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors" onclick="toggleSolution(this)">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center mr-4">
                                <span class="text-gray-600 font-bold text-sm">🌙</span>
                            </div>
                            <span class="text-lg font-semibold text-gray-900">Dark mode not working or partially applied</span>
                        </div>
                        <svg class="toggle-icon w-5 h-5 text-gray-500 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div class="solution-content px-6 pb-4">
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h4 class="font-semibold text-gray-900 mb-3">Troubleshooting Steps:</h4>
                            <ol class="list-decimal list-inside space-y-2 text-gray-600 text-sm">
                                <li>Check if dark mode is enabled in Better Canvas settings</li>
                                <li>Verify system dark mode settings (if auto-switch is enabled)</li>
                                <li>Clear browser cache and refresh Canvas page</li>
                                <li>Disable other dark mode extensions temporarily</li>
                                <li>Try switching to a different theme and back to dark mode</li>
                            </ol>
                            <div class="mt-4 p-3 bg-gray-50 rounded border border-gray-200">
                                <p class="text-gray-800 text-sm">
                                    <strong>Note:</strong> Some Canvas elements may not support dark mode due to Canvas's own styling restrictions
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Theme Issue 3: Custom Theme Problems -->
                <div class="troubleshoot-item bg-white rounded-lg border border-gray-200 shadow-sm">
                    <button class="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors" onclick="toggleSolution(this)">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                                <span class="text-blue-600 font-bold text-sm">🛠</span>
                            </div>
                            <span class="text-lg font-semibold text-gray-900">Custom theme not saving or applying correctly</span>
                        </div>
                        <svg class="toggle-icon w-5 h-5 text-gray-500 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div class="solution-content px-6 pb-4">
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h4 class="font-semibold text-gray-900 mb-3">Resolution Steps:</h4>
                            <div class="space-y-3">
                                <div class="flex items-start">
                                    <div class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3"></div>
                                    <div>
                                        <strong class="text-gray-900">Storage permissions:</strong>
                                        <p class="text-gray-600 text-sm">Ensure browser allows extension to store data</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <div class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3"></div>
                                    <div>
                                        <strong class="text-gray-900">Theme validation:</strong>
                                        <p class="text-gray-600 text-sm">Check if custom theme code is valid CSS</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <div class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3"></div>
                                    <div>
                                        <strong class="text-gray-900">Browser sync:</strong>
                                        <p class="text-gray-600 text-sm">Disable browser sync temporarily if themes keep reverting</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Browser Compatibility -->
    <section id="compatibility" class="py-16 lg:py-24 bg-gray-50">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                    Browser Compatibility Issues
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Resolve compatibility problems with different browsers and versions.
                </p>
            </div>

            <!-- Compatibility Table -->
            <div class="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden mb-8">
                <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">Supported Browsers & Versions</h3>
                </div>
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Browser</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Minimum Version</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Recommended</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <img src="https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v9/icons/googlechrome.svg" alt="Chrome" class="w-5 h-5 mr-3">
                                        <span class="text-sm font-medium text-gray-900">Google Chrome</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">88+</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Latest</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                        Fully Supported
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <img src="https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v9/icons/firefox.svg" alt="Firefox" class="w-5 h-5 mr-3">
                                        <span class="text-sm font-medium text-gray-900">Mozilla Firefox</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">78+</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Latest</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                        Fully Supported
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <span class="w-5 h-5 mr-3 text-gray-400">🌐</span>
                                        <span class="text-sm font-medium text-gray-900">Microsoft Edge</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">88+</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Latest</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                        Limited Support
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <span class="w-5 h-5 mr-3 text-gray-400">📱</span>
                                        <span class="text-sm font-medium text-gray-900">Mobile Browsers</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">N/A</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Desktop Only</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                        Not Supported
                                    </span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Common Browser Issues -->
            <div class="space-y-6">
                <div class="troubleshoot-item bg-white rounded-lg border border-gray-200 shadow-sm">
                    <button class="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors" onclick="toggleSolution(this)">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center mr-4">
                                <span class="text-red-600 font-bold text-sm">⚠</span>
                            </div>
                            <span class="text-lg font-semibold text-gray-900">Better Canvas not working on older browser versions</span>
                        </div>
                        <svg class="toggle-icon w-5 h-5 text-gray-500 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div class="solution-content px-6 pb-4">
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h4 class="font-semibold text-gray-900 mb-3">Update Required:</h4>
                            <p class="text-gray-600 text-sm mb-3">
                                Better Canvas requires modern browser features that are only available in recent versions.
                            </p>
                            <div class="space-y-2">
                                <div class="flex items-center text-sm">
                                    <span class="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                                    <span><strong>Chrome:</strong> Update to version 88 or later</span>
                                </div>
                                <div class="flex items-center text-sm">
                                    <span class="w-2 h-2 bg-orange-500 rounded-full mr-3"></span>
                                    <span><strong>Firefox:</strong> Update to version 78 or later</span>
                                </div>
                            </div>
                            <div class="mt-4 p-3 bg-blue-50 rounded border border-blue-200">
                                <p class="text-blue-800 text-sm">
                                    <strong>How to update:</strong> Browser menu → Help → About [Browser Name] → Automatic update will start
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Support -->
    <section class="py-16 lg:py-24 bg-white">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                Still Need Help?
            </h2>
            <p class="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
                If you've tried all the troubleshooting steps and Better Canvas extension is still not working, our support team is here to help.
            </p>

            <div class="grid md:grid-cols-3 gap-8 mb-12">
                <!-- Contact Support -->
                <div class="bg-blue-50 rounded-lg p-6 border border-blue-200">
                    <div class="text-3xl mb-4">📧</div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Contact Support</h3>
                    <p class="text-gray-600 text-sm mb-4">
                        Get personalized help from our support team
                    </p>
                    <a href="contact.html" class="text-blue-600 hover:text-blue-700 font-medium">
                        Send Message
                    </a>
                </div>

                <!-- Community Help -->
                <div class="bg-green-50 rounded-lg p-6 border border-green-200">
                    <div class="text-3xl mb-4">💬</div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Community Help</h3>
                    <p class="text-gray-600 text-sm mb-4">
                        Ask questions in our community forum
                    </p>
                    <a href="https://github.com/UseBetterCanvas/bettercanvas/discussions" target="_blank" rel="noopener noreferrer" class="text-green-600 hover:text-green-700 font-medium">
                        Join Discussion
                    </a>
                </div>

                <!-- Report Bug -->
                <div class="bg-red-50 rounded-lg p-6 border border-red-200">
                    <div class="text-3xl mb-4">🐛</div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Report Bug</h3>
                    <p class="text-gray-600 text-sm mb-4">
                        Found a bug? Help us improve Better Canvas
                    </p>
                    <a href="https://github.com/UseBetterCanvas/bettercanvas/issues" target="_blank" rel="noopener noreferrer" class="text-red-600 hover:text-red-700 font-medium">
                        Report Issue
                    </a>
                </div>
            </div>

            <!-- Quick Download CTA -->
            <div class="bg-gray-50 border border-gray-200 rounded-lg p-6">
                <h4 class="font-semibold text-gray-900 mb-2">Need to Reinstall Better Canvas?</h4>
                <p class="text-gray-600 text-sm mb-4">
                    Sometimes a fresh installation can resolve persistent issues.
                </p>
                <div class="flex flex-col sm:flex-row gap-3 justify-center">
                    <a href="download.html" class="bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-6 rounded-lg transition-colors">
                        Download Again
                    </a>
                    <a href="tutorials.html" class="bg-white hover:bg-gray-50 text-gray-600 font-medium py-2 px-6 rounded-lg border border-gray-300 transition-colors">
                        Installation Guide
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid md:grid-cols-4 gap-8">
                <!-- Brand -->
                <div class="md:col-span-1">
                    <div class="flex items-center space-x-2 mb-4">
                        <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-sm">BC</span>
                        </div>
                        <span class="text-xl font-bold">Better Canvas</span>
                    </div>
                    <p class="text-gray-400 text-sm leading-relaxed">
                        Transform your Canvas learning experience with Better Canvas extension - featuring dark mode, better canvas themes, GPA calculator, and productivity tools.
                    </p>
                </div>

                <!-- Quick Links -->
                <div>
                    <h3 class="font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-2 text-sm">
                        <li><a href="download.html" class="text-gray-400 hover:text-white transition-colors">Download</a></li>
                        <li><a href="features.html" class="text-gray-400 hover:text-white transition-colors">Features</a></li>
                        <li><a href="themes.html" class="text-gray-400 hover:text-white transition-colors">Themes</a></li>
                        <li><a href="tutorials.html" class="text-gray-400 hover:text-white transition-colors">Tutorials</a></li>
                    </ul>
                </div>

                <!-- Support -->
                <div>
                    <h3 class="font-semibold mb-4">Support</h3>
                    <ul class="space-y-2 text-sm">
                        <li><a href="faq.html" class="text-gray-400 hover:text-white transition-colors">FAQ</a></li>
                        <li><a href="troubleshooting.html" class="text-gray-400 hover:text-white transition-colors">Troubleshooting</a></li>
                        <li><a href="contact.html" class="text-gray-400 hover:text-white transition-colors">Contact Us</a></li>
                        <li><a href="changelog.html" class="text-gray-400 hover:text-white transition-colors">Changelog</a></li>
                    </ul>
                </div>

                <!-- Official Links -->
                <div>
                    <h3 class="font-semibold mb-4">Official Links</h3>
                    <ul class="space-y-2 text-sm">
                        <li><a href="https://chrome.google.com/webstore/detail/better-canvas/cndibmoanboadcifjkjbdpjgfedanolh" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition-colors">Chrome Store</a></li>
                        <li><a href="https://addons.mozilla.org/en-US/firefox/addon/better-canvas/" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition-colors">Firefox Add-ons</a></li>
                        <li><a href="https://github.com/UseBetterCanvas/bettercanvas" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition-colors">GitHub</a></li>
                        <li><a href="https://diditupe.dev/bettercanvas/" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition-colors">Official Site</a></li>
                    </ul>
                </div>
            </div>

            <!-- Bottom Bar -->
            <div class="border-t border-gray-800 mt-12 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <div class="text-sm text-gray-400 mb-4 md:mb-0">
                        © 2024 Better Canvas Resources. All rights reserved.
                    </div>
                    <div class="flex space-x-6 text-sm">
                        <a href="privacy.html" class="text-gray-400 hover:text-white transition-colors">Privacy Policy</a>
                        <a href="terms.html" class="text-gray-400 hover:text-white transition-colors">Terms of Use</a>
                        <a href="disclaimer.html" class="text-gray-400 hover:text-white transition-colors">Disclaimer</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script>
        function toggleMobileMenu() {
            const menu = document.getElementById('mobile-menu');
            menu.classList.toggle('hidden');
        }

        function toggleSolution(element) {
            const content = element.nextElementSibling;
            const icon = element.querySelector('.toggle-icon');

            content.classList.toggle('open');
            icon.style.transform = content.classList.contains('open') ? 'rotate(180deg)' : 'rotate(0deg)';
        }

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
