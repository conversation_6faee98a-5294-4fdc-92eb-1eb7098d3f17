export default function HowItWorks() {
  const steps = [
    {
      icon: '⬇️',
      title: '1. Download Extension',
      description: 'Install BetterCanvas from Chrome Web Store or Firefox Add-ons in seconds.',
      image: 'Browser extension installation interface'
    },
    {
      icon: '⚙️',
      title: '2. Quick Setup',
      description: 'Open Canvas and configure your preferences. No account required.',
      image: 'Settings configuration panel'
    },
    {
      icon: '🎉',
      title: '3. Transform Canvas',
      description: 'Enjoy a beautiful, productive Canvas experience with all features enabled.',
      image: 'Enhanced Canvas dashboard'
    }
  ]

  return (
    <section className="section-padding bg-gray-50">
      <div className="container-custom">
        <div className="text-center mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            Get Started in 3 Simple Steps
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Transform your Canvas experience in less than 2 minutes. No technical knowledge required.
          </p>
        </div>

        <div className="grid lg:grid-cols-3 gap-8 lg:gap-12">
          {steps.map((step, index) => (
            <div key={index} className="text-center">
              {/* Step Icon */}
              <div className="relative mb-6">
                <div className="w-20 h-20 bg-primary-600 rounded-full flex items-center justify-center text-3xl mx-auto mb-4">
                  {step.icon}
                </div>
                {index < steps.length - 1 && (
                  <div className="hidden lg:block absolute top-10 left-full w-full h-0.5 bg-gray-300 transform -translate-y-1/2"></div>
                )}
              </div>

              {/* Step Content */}
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                {step.title}
              </h3>
              <p className="text-gray-600 leading-relaxed mb-6">
                {step.description}
              </p>

              {/* Step Visual */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 h-32 flex items-center justify-center">
                <div className="text-gray-400 text-sm text-center">
                  {step.image}
                  <br />
                  <span className="text-xs">[Screenshot placeholder]</span>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Additional Info */}
        <div className="mt-16 text-center">
          <div className="bg-white rounded-xl p-8 shadow-sm border border-gray-200 max-w-2xl mx-auto">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">
              Need Help Getting Started?
            </h3>
            <p className="text-gray-600 mb-4">
              Check out our detailed installation guide and video tutorials.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <a href="/tutorials/installation" className="btn-secondary">
                Installation Guide
              </a>
              <a href="/tutorials" className="btn-primary">
                Watch Tutorials
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
