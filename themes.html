<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Better Canvas Themes</title>
    <meta name="description" content="Explore premium Better Canvas themes: free, downloadable, and easy to import. Features popular styles with tutorials to create or remove themes for a personalized experience.">

    <!-- Open Graph -->
    <meta property="og:title" content="Better Canvas Themes - 50+ Beautiful Themes for Canvas LMS">
    <meta property="og:description" content="Discover and download beautiful better canvas themes for Canvas LMS. Transform your Canvas experience with custom themes designed by the community.">
    <meta property="og:type" content="website">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                        }
                    }
                }
            }
        }
    </script>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        body { font-family: 'Inter', system-ui, sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .theme-card { transition: all 0.3s ease; }
        .theme-card:hover { transform: translateY(-4px); }
    </style>

    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "CollectionPage",
        "name": "Better Canvas Themes",
        "description": "Collection of 50+ better canvas themes for Canvas LMS including dark themes, colorful themes, and minimalist designs",
        "about": {
            "@type": "SoftwareApplication",
            "name": "Better Canvas",
            "applicationCategory": "BrowserApplication"
        },
        "hasPart": [
            {
                "@type": "CreativeWork",
                "name": "Dark Ocean Theme",
                "description": "Professional dark blue theme for Better Canvas"
            },
            {
                "@type": "CreativeWork",
                "name": "Purple Dream Theme",
                "description": "Vibrant purple gradient theme for Better Canvas"
            },
            {
                "@type": "CreativeWork",
                "name": "Forest Green Theme",
                "description": "Calming green nature-inspired theme for Better Canvas"
            }
        ]
    }
    </script>
</head>
<body class="antialiased">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="index.html" class="flex items-center space-x-2">
                        <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-sm">BC</span>
                        </div>
                        <span class="text-xl font-bold text-gray-900">Better Canvas</span>
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <nav class="hidden md:flex items-center space-x-8">
                    <a href="index.html" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">Home</a>
                    <a href="download.html" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">Download</a>
                    <a href="features.html" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">Features</a>
                    <a href="tutorials.html" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">Tutorials</a>
                    <a href="themes.html" class="text-primary-600 font-medium">Themes</a>
                    <a href="faq.html" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">FAQ</a>
                </nav>

                <!-- CTA Button -->
                <div class="hidden md:flex">
                    <a href="download.html" class="bg-primary-600 hover:bg-primary-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200">
                        Download Now
                    </a>
                </div>

                <!-- Mobile menu button -->
                <button class="md:hidden p-2" onclick="toggleMobileMenu()">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>

            <!-- Mobile Navigation -->
            <div id="mobile-menu" class="hidden md:hidden py-4 border-t border-gray-200">
                <nav class="flex flex-col space-y-4">
                    <a href="index.html" class="text-gray-600 hover:text-gray-900 font-medium">Home</a>
                    <a href="download.html" class="text-gray-600 hover:text-gray-900 font-medium">Download</a>
                    <a href="features.html" class="text-gray-600 hover:text-gray-900 font-medium">Features</a>
                    <a href="tutorials.html" class="text-gray-600 hover:text-gray-900 font-medium">Tutorials</a>
                    <a href="themes.html" class="text-primary-600 font-medium">Themes</a>
                    <a href="faq.html" class="text-gray-600 hover:text-gray-900 font-medium">FAQ</a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="py-16 lg:py-24 bg-gradient-to-br from-purple-50 to-pink-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h1 class="text-4xl lg:text-6xl font-bold text-gray-900 leading-tight mb-6">
                    Better Canvas Themes Gallery
                </h1>
                <p class="text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
                    Discover 50+ beautiful better canvas themes designed by our community. Transform your Canvas LMS experience with
                    stunning themes ranging from professional dark modes to vibrant colorful designs.
                </p>

                <!-- Theme Stats -->
                <div class="flex flex-col sm:flex-row items-center justify-center space-y-2 sm:space-y-0 sm:space-x-8 mb-12">
                    <div class="flex items-center">
                        <span class="text-3xl font-bold text-purple-600 mr-2">50+</span>
                        <span class="text-gray-600 font-medium">Better Canvas Themes</span>
                    </div>
                    <div class="text-gray-600">
                        <span class="font-semibold">100%</span> Free to Use
                    </div>
                    <div class="text-gray-600">
                        <span class="font-semibold">Community</span> Created
                    </div>
                </div>

                <!-- Quick Install Notice -->
                <div class="bg-purple-100 border border-purple-200 rounded-lg p-4 max-w-2xl mx-auto mb-8">
                    <div class="flex items-center justify-center">
                        <svg class="w-5 h-5 text-purple-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-purple-800 text-sm font-medium">
                            Install Better Canvas extension first to use these themes
                        </span>
                    </div>
                </div>

                <!-- Filter Buttons -->
                <div class="flex flex-wrap justify-center gap-3 mb-8">
                    <button onclick="filterThemes('all')" class="filter-btn active bg-purple-600 text-white font-medium py-2 px-4 rounded-lg transition-colors text-sm">
                        All Themes
                    </button>
                    <button onclick="filterThemes('dark')" class="filter-btn bg-white hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 rounded-lg border border-gray-300 transition-colors text-sm">
                        Dark Themes
                    </button>
                    <button onclick="filterThemes('colorful')" class="filter-btn bg-white hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 rounded-lg border border-gray-300 transition-colors text-sm">
                        Colorful
                    </button>
                    <button onclick="filterThemes('minimal')" class="filter-btn bg-white hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 rounded-lg border border-gray-300 transition-colors text-sm">
                        Minimal
                    </button>
                    <button onclick="filterThemes('popular')" class="filter-btn bg-white hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 rounded-lg border border-gray-300 transition-colors text-sm">
                        Most Popular
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Better Canvas Themes -->
    <section class="py-16 lg:py-24 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                    Featured Better Canvas Themes
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Our most popular better canvas themes, carefully selected and tested by thousands of students worldwide.
                </p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8" id="themes-grid">
                <!-- Theme 1: Dark Ocean -->
                <div class="theme-card bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden" data-category="dark popular">
                    <div class="relative">
                        <!-- Theme Preview -->
                        <div class="h-48 bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 p-6">
                            <div class="bg-slate-800 rounded-lg p-4 h-full">
                                <div class="flex items-center justify-between mb-3">
                                    <div class="w-16 h-3 bg-blue-400 rounded"></div>
                                    <div class="w-8 h-8 bg-blue-500 rounded-full"></div>
                                </div>
                                <div class="space-y-2">
                                    <div class="w-full h-2 bg-slate-600 rounded"></div>
                                    <div class="w-3/4 h-2 bg-slate-600 rounded"></div>
                                    <div class="w-1/2 h-2 bg-slate-600 rounded"></div>
                                </div>
                                <div class="mt-4 flex space-x-2">
                                    <div class="w-12 h-8 bg-blue-600 rounded text-xs"></div>
                                    <div class="w-12 h-8 bg-slate-600 rounded text-xs"></div>
                                </div>
                            </div>
                        </div>
                        <!-- Popular Badge -->
                        <div class="absolute top-4 right-4 bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                            #1 Popular
                        </div>
                    </div>

                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">Dark Ocean</h3>
                        <p class="text-gray-600 text-sm mb-4">
                            Professional dark blue theme perfect for late-night studying. Easy on the eyes with excellent contrast.
                        </p>

                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center space-x-4 text-sm text-gray-500">
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                    </svg>
                                    4.9
                                </span>
                                <span>12.5k downloads</span>
                            </div>
                            <span class="bg-slate-100 text-slate-800 px-2 py-1 rounded text-xs font-medium">Dark</span>
                        </div>

                        <button onclick="installTheme('dark-ocean')" class="w-full bg-slate-800 hover:bg-slate-900 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200">
                            Install Theme
                        </button>
                    </div>
                </div>

                <!-- Theme 2: Purple Dream -->
                <div class="theme-card bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden" data-category="colorful popular">
                    <div class="relative">
                        <!-- Theme Preview -->
                        <div class="h-48 bg-gradient-to-br from-purple-600 via-pink-500 to-purple-700 p-6">
                            <div class="bg-white bg-opacity-20 backdrop-blur-sm rounded-lg p-4 h-full">
                                <div class="flex items-center justify-between mb-3">
                                    <div class="w-16 h-3 bg-white bg-opacity-80 rounded"></div>
                                    <div class="w-8 h-8 bg-white bg-opacity-60 rounded-full"></div>
                                </div>
                                <div class="space-y-2">
                                    <div class="w-full h-2 bg-white bg-opacity-40 rounded"></div>
                                    <div class="w-3/4 h-2 bg-white bg-opacity-40 rounded"></div>
                                    <div class="w-1/2 h-2 bg-white bg-opacity-40 rounded"></div>
                                </div>
                                <div class="mt-4 flex space-x-2">
                                    <div class="w-12 h-8 bg-white bg-opacity-60 rounded text-xs"></div>
                                    <div class="w-12 h-8 bg-white bg-opacity-30 rounded text-xs"></div>
                                </div>
                            </div>
                        </div>
                        <!-- Trending Badge -->
                        <div class="absolute top-4 right-4 bg-pink-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                            Trending
                        </div>
                    </div>

                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">Purple Dream</h3>
                        <p class="text-gray-600 text-sm mb-4">
                            Vibrant purple gradient theme that adds energy and creativity to your Canvas experience.
                        </p>

                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center space-x-4 text-sm text-gray-500">
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                    </svg>
                                    4.7
                                </span>
                                <span>8.2k downloads</span>
                            </div>
                            <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded text-xs font-medium">Colorful</span>
                        </div>

                        <button onclick="installTheme('purple-dream')" class="w-full bg-purple-600 hover:bg-purple-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200">
                            Install Theme
                        </button>
                    </div>
                </div>

                <!-- Theme 3: Forest Green -->
                <div class="theme-card bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden" data-category="colorful">
                    <div class="relative">
                        <!-- Theme Preview -->
                        <div class="h-48 bg-gradient-to-br from-green-600 via-emerald-500 to-green-700 p-6">
                            <div class="bg-white bg-opacity-20 backdrop-blur-sm rounded-lg p-4 h-full">
                                <div class="flex items-center justify-between mb-3">
                                    <div class="w-16 h-3 bg-white bg-opacity-80 rounded"></div>
                                    <div class="w-8 h-8 bg-white bg-opacity-60 rounded-full"></div>
                                </div>
                                <div class="space-y-2">
                                    <div class="w-full h-2 bg-white bg-opacity-40 rounded"></div>
                                    <div class="w-3/4 h-2 bg-white bg-opacity-40 rounded"></div>
                                    <div class="w-1/2 h-2 bg-white bg-opacity-40 rounded"></div>
                                </div>
                                <div class="mt-4 flex space-x-2">
                                    <div class="w-12 h-8 bg-white bg-opacity-60 rounded text-xs"></div>
                                    <div class="w-12 h-8 bg-white bg-opacity-30 rounded text-xs"></div>
                                </div>
                            </div>
                        </div>
                        <!-- New Badge -->
                        <div class="absolute top-4 right-4 bg-green-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                            Calming
                        </div>
                    </div>

                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">Forest Green</h3>
                        <p class="text-gray-600 text-sm mb-4">
                            Nature-inspired green theme that promotes focus and reduces eye strain during long study sessions.
                        </p>

                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center space-x-4 text-sm text-gray-500">
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                    </svg>
                                    4.6
                                </span>
                                <span>6.8k downloads</span>
                            </div>
                            <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-medium">Nature</span>
                        </div>

                        <button onclick="installTheme('forest-green')" class="w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200">
                            Install Theme
                        </button>
                    </div>
                </div>

                <!-- Theme 4: Sunset Orange -->
                <div class="theme-card bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden" data-category="colorful">
                    <div class="relative">
                        <!-- Theme Preview -->
                        <div class="h-48 bg-gradient-to-br from-orange-500 via-red-500 to-pink-500 p-6">
                            <div class="bg-white bg-opacity-20 backdrop-blur-sm rounded-lg p-4 h-full">
                                <div class="flex items-center justify-between mb-3">
                                    <div class="w-16 h-3 bg-white bg-opacity-80 rounded"></div>
                                    <div class="w-8 h-8 bg-white bg-opacity-60 rounded-full"></div>
                                </div>
                                <div class="space-y-2">
                                    <div class="w-full h-2 bg-white bg-opacity-40 rounded"></div>
                                    <div class="w-3/4 h-2 bg-white bg-opacity-40 rounded"></div>
                                    <div class="w-1/2 h-2 bg-white bg-opacity-40 rounded"></div>
                                </div>
                                <div class="mt-4 flex space-x-2">
                                    <div class="w-12 h-8 bg-white bg-opacity-60 rounded text-xs"></div>
                                    <div class="w-12 h-8 bg-white bg-opacity-30 rounded text-xs"></div>
                                </div>
                            </div>
                        </div>
                        <!-- Warm Badge -->
                        <div class="absolute top-4 right-4 bg-orange-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                            Energizing
                        </div>
                    </div>

                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">Sunset Orange</h3>
                        <p class="text-gray-600 text-sm mb-4">
                            Warm sunset-inspired theme that brings energy and motivation to your study sessions.
                        </p>

                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center space-x-4 text-sm text-gray-500">
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                    </svg>
                                    4.5
                                </span>
                                <span>5.1k downloads</span>
                            </div>
                            <span class="bg-orange-100 text-orange-800 px-2 py-1 rounded text-xs font-medium">Warm</span>
                        </div>

                        <button onclick="installTheme('sunset-orange')" class="w-full bg-orange-500 hover:bg-orange-600 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200">
                            Install Theme
                        </button>
                    </div>
                </div>

                <!-- Theme 5: Minimalist White -->
                <div class="theme-card bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden" data-category="minimal popular">
                    <div class="relative">
                        <!-- Theme Preview -->
                        <div class="h-48 bg-gradient-to-br from-gray-50 to-white p-6">
                            <div class="bg-white border border-gray-200 rounded-lg p-4 h-full shadow-sm">
                                <div class="flex items-center justify-between mb-3">
                                    <div class="w-16 h-3 bg-gray-300 rounded"></div>
                                    <div class="w-8 h-8 bg-gray-200 rounded-full"></div>
                                </div>
                                <div class="space-y-2">
                                    <div class="w-full h-2 bg-gray-200 rounded"></div>
                                    <div class="w-3/4 h-2 bg-gray-200 rounded"></div>
                                    <div class="w-1/2 h-2 bg-gray-200 rounded"></div>
                                </div>
                                <div class="mt-4 flex space-x-2">
                                    <div class="w-12 h-8 bg-blue-500 rounded text-xs"></div>
                                    <div class="w-12 h-8 bg-gray-200 rounded text-xs"></div>
                                </div>
                            </div>
                        </div>
                        <!-- Clean Badge -->
                        <div class="absolute top-4 right-4 bg-gray-600 text-white px-2 py-1 rounded-full text-xs font-semibold">
                            Clean
                        </div>
                    </div>

                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">Minimalist White</h3>
                        <p class="text-gray-600 text-sm mb-4">
                            Clean, distraction-free design that enhances focus and readability for serious studying.
                        </p>

                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center space-x-4 text-sm text-gray-500">
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                    </svg>
                                    4.8
                                </span>
                                <span>9.3k downloads</span>
                            </div>
                            <span class="bg-gray-100 text-gray-800 px-2 py-1 rounded text-xs font-medium">Minimal</span>
                        </div>

                        <button onclick="installTheme('minimalist-white')" class="w-full bg-gray-600 hover:bg-gray-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200">
                            Install Theme
                        </button>
                    </div>
                </div>

                <!-- More Themes Coming Soon -->
                <div class="bg-gray-50 rounded-2xl border-2 border-dashed border-gray-300 p-8 text-center">
                    <div class="text-4xl mb-4">🎨</div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">More Themes Coming Soon</h3>
                    <p class="text-gray-600 text-sm mb-4">
                        We're constantly adding new better canvas themes. Have a theme idea? Let us know!
                    </p>
                    <a href="contact.html" class="text-primary-600 hover:text-primary-700 font-medium inline-flex items-center">
                        Suggest a Theme
                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Theme Installation Guide -->
    <section class="py-16 lg:py-24 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                    How to Install Better Canvas Themes
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Follow these simple steps to install and use better canvas themes with your Better Canvas extension.
                </p>
            </div>

            <div class="grid lg:grid-cols-3 gap-8">
                <!-- Step 1 -->
                <div class="text-center">
                    <div class="w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-6">
                        <span class="text-white font-bold text-xl">1</span>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">
                        Install Better Canvas Extension
                    </h3>
                    <p class="text-gray-600 leading-relaxed mb-6">
                        First, make sure you have Better Canvas extension installed from Chrome Web Store or Firefox Add-ons.
                    </p>
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 h-32 flex items-center justify-center">
                        <div class="text-gray-400 text-sm text-center">
                            [Screenshot: Extension Installation]
                            <br />
                            <span class="text-xs">Better Canvas in browser store</span>
                        </div>
                    </div>
                </div>

                <!-- Step 2 -->
                <div class="text-center">
                    <div class="w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-6">
                        <span class="text-white font-bold text-xl">2</span>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">
                        Open Better Canvas Settings
                    </h3>
                    <p class="text-gray-600 leading-relaxed mb-6">
                        Click the Better Canvas icon in your browser and navigate to the Themes section in settings.
                    </p>
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 h-32 flex items-center justify-center">
                        <div class="text-gray-400 text-sm text-center">
                            [Screenshot: Extension Settings]
                            <br />
                            <span class="text-xs">Themes section interface</span>
                        </div>
                    </div>
                </div>

                <!-- Step 3 -->
                <div class="text-center">
                    <div class="w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-6">
                        <span class="text-white font-bold text-xl">3</span>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">
                        Apply Your Theme
                    </h3>
                    <p class="text-gray-600 leading-relaxed mb-6">
                        Browse available themes, preview them, and click "Apply" to instantly transform your Canvas experience.
                    </p>
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 h-32 flex items-center justify-center">
                        <div class="text-gray-400 text-sm text-center">
                            [Screenshot: Theme Applied]
                            <br />
                            <span class="text-xs">Canvas with new theme</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Help Section -->
            <div class="mt-16 text-center">
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 max-w-2xl mx-auto">
                    <h4 class="font-semibold text-blue-900 mb-2">Need Help Installing Themes?</h4>
                    <p class="text-blue-800 text-sm mb-4">
                        Check our detailed tutorial or contact our support team for assistance with better canvas themes installation.
                    </p>
                    <div class="flex flex-col sm:flex-row gap-3 justify-center">
                        <a href="tutorials.html#themes" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors text-sm">
                            View Tutorial
                        </a>
                        <a href="contact.html" class="bg-white hover:bg-gray-50 text-blue-600 font-medium py-2 px-4 rounded-lg border border-blue-300 transition-colors text-sm">
                            Get Support
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Final CTA Section -->
    <section class="py-16 lg:py-24 gradient-bg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center text-white">
                <h2 class="text-3xl lg:text-4xl font-bold mb-4">
                    Ready to Transform Canvas with Better Canvas Themes?
                </h2>
                <p class="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
                    Install Better Canvas extension now and get instant access to 50+ beautiful better canvas themes.
                    Transform your Canvas experience with just one click.
                </p>

                <!-- CTA Buttons -->
                <div class="flex flex-col sm:flex-row gap-4 justify-center mb-8">
                    <a href="download.html" class="bg-white text-gray-900 hover:bg-gray-100 font-semibold py-4 px-8 rounded-lg transition-colors duration-200 inline-flex items-center justify-center text-lg">
                        <img src="https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v9/icons/googlechrome.svg" alt="Chrome" class="w-6 h-6 mr-3">
                        Better Canvas Chrome Extension
                    </a>
                    <a href="download.html" class="bg-transparent border-2 border-white text-white hover:bg-white hover:text-gray-900 font-semibold py-4 px-8 rounded-lg transition-colors duration-200 inline-flex items-center justify-center text-lg">
                        <img src="https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v9/icons/firefox.svg" alt="Firefox" class="w-6 h-6 mr-3 filter brightness-0 invert">
                        Better Canvas Firefox
                    </a>
                </div>

                <!-- Theme Categories -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm opacity-90">
                    <div class="flex items-center justify-center">
                        <span class="mr-2">🌙</span>
                        Dark Themes
                    </div>
                    <div class="flex items-center justify-center">
                        <span class="mr-2">🎨</span>
                        Colorful Themes
                    </div>
                    <div class="flex items-center justify-center">
                        <span class="mr-2">⚪</span>
                        Minimal Themes
                    </div>
                    <div class="flex items-center justify-center">
                        <span class="mr-2">✨</span>
                        50+ Options
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid md:grid-cols-4 gap-8">
                <!-- Brand -->
                <div class="md:col-span-1">
                    <div class="flex items-center space-x-2 mb-4">
                        <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-sm">BC</span>
                        </div>
                        <span class="text-xl font-bold">Better Canvas</span>
                    </div>
                    <p class="text-gray-400 text-sm leading-relaxed">
                        Transform your Canvas learning experience with Better Canvas extension - featuring dark mode, better canvas themes, GPA calculator, and productivity tools.
                    </p>
                </div>

                <!-- Quick Links -->
                <div>
                    <h3 class="font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-2 text-sm">
                        <li><a href="download.html" class="text-gray-400 hover:text-white transition-colors">Download</a></li>
                        <li><a href="features.html" class="text-gray-400 hover:text-white transition-colors">Features</a></li>
                        <li><a href="themes.html" class="text-gray-400 hover:text-white transition-colors">Themes</a></li>
                        <li><a href="tutorials.html" class="text-gray-400 hover:text-white transition-colors">Tutorials</a></li>
                    </ul>
                </div>

                <!-- Support -->
                <div>
                    <h3 class="font-semibold mb-4">Support</h3>
                    <ul class="space-y-2 text-sm">
                        <li><a href="faq.html" class="text-gray-400 hover:text-white transition-colors">FAQ</a></li>
                        <li><a href="troubleshooting.html" class="text-gray-400 hover:text-white transition-colors">Troubleshooting</a></li>
                        <li><a href="contact.html" class="text-gray-400 hover:text-white transition-colors">Contact Us</a></li>
                        <li><a href="changelog.html" class="text-gray-400 hover:text-white transition-colors">Changelog</a></li>
                    </ul>
                </div>

                <!-- Official Links -->
                <div>
                    <h3 class="font-semibold mb-4">Official Links</h3>
                    <ul class="space-y-2 text-sm">
                        <li><a href="https://chrome.google.com/webstore/detail/better-canvas/cndibmoanboadcifjkjbdpjgfedanolh" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition-colors">Chrome Store</a></li>
                        <li><a href="https://addons.mozilla.org/en-US/firefox/addon/better-canvas/" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition-colors">Firefox Add-ons</a></li>
                        <li><a href="https://github.com/UseBetterCanvas/bettercanvas" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition-colors">GitHub</a></li>
                        <li><a href="https://diditupe.dev/bettercanvas/" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition-colors">Official Site</a></li>
                    </ul>
                </div>
            </div>

            <!-- Bottom Bar -->
            <div class="border-t border-gray-800 mt-12 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <div class="text-sm text-gray-400 mb-4 md:mb-0">
                        © 2024 Better Canvas Resources. All rights reserved.
                    </div>
                    <div class="flex space-x-6 text-sm">
                        <a href="privacy.html" class="text-gray-400 hover:text-white transition-colors">Privacy Policy</a>
                        <a href="terms.html" class="text-gray-400 hover:text-white transition-colors">Terms of Use</a>
                        <a href="disclaimer.html" class="text-gray-400 hover:text-white transition-colors">Disclaimer</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script>
        function toggleMobileMenu() {
            const menu = document.getElementById('mobile-menu');
            menu.classList.toggle('hidden');
        }

        function filterThemes(category) {
            // Update active filter button
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active', 'bg-purple-600', 'text-white');
                btn.classList.add('bg-white', 'text-gray-700');
            });
            event.target.classList.add('active', 'bg-purple-600', 'text-white');
            event.target.classList.remove('bg-white', 'text-gray-700');

            // Filter theme cards
            const themeCards = document.querySelectorAll('.theme-card');
            themeCards.forEach(card => {
                if (category === 'all' || card.dataset.category.includes(category)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        }

        function installTheme(themeId) {
            // Show installation modal or redirect to installation guide
            alert(`To install the ${themeId} theme:\n\n1. Make sure Better Canvas extension is installed\n2. Open Canvas in your browser\n3. Click the Better Canvas icon\n4. Go to Themes section\n5. Search for "${themeId}" and click Install\n\nNeed help? Check our installation guide!`);
        }
    </script>
</body>
</html>
