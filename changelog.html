<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Better Canvas Changelog - Version History & Updates | Better Canvas Extension</title>
    <meta name="description" content="Stay updated with Better Canvas extension changelog. View latest features, better canvas themes updates, bug fixes, and improvements in each version release.">
    <meta name="keywords" content="better canvas changelog, better canvas updates, better canvas version history, better canvas new features, better canvas themes updates, canvas extension updates, better canvas release notes">

    <!-- Open Graph -->
    <meta property="og:title" content="Better Canvas Changelog - Version History & Updates">
    <meta property="og:description" content="Latest updates and improvements to Better Canvas extension including new features, theme updates, and bug fixes.">
    <meta property="og:type" content="website">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                        }
                    }
                }
            }
        }
    </script>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        body { font-family: 'Inter', system-ui, sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .version-card { transition: all 0.3s ease; }
        .version-card:hover { transform: translateY(-2px); }
        .timeline-line {
            position: absolute;
            left: 1.5rem;
            top: 4rem;
            bottom: 0;
            width: 2px;
            background: linear-gradient(to bottom, #e5e7eb, transparent);
        }
    </style>

    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebPage",
        "name": "Better Canvas Changelog",
        "description": "Version history and updates for Better Canvas extension",
        "about": {
            "@type": "SoftwareApplication",
            "name": "Better Canvas",
            "applicationCategory": "BrowserApplication"
        },
        "mainEntity": {
            "@type": "SoftwareApplication",
            "name": "Better Canvas",
            "applicationCategory": "BrowserApplication",
            "operatingSystem": "Chrome, Firefox",
            "releaseNotes": "Regular updates with new features, theme improvements, and bug fixes"
        }
    }
    </script>
</head>
<body class="antialiased">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="index.html" class="flex items-center space-x-2">
                        <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-sm">BC</span>
                        </div>
                        <span class="text-xl font-bold text-gray-900">Better Canvas</span>
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <nav class="hidden md:flex items-center space-x-8">
                    <a href="index.html" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">Home</a>
                    <a href="download.html" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">Download</a>
                    <a href="features.html" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">Features</a>
                    <a href="tutorials.html" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">Tutorials</a>
                    <a href="themes.html" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">Themes</a>
                    <a href="faq.html" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">FAQ</a>
                </nav>

                <!-- CTA Button -->
                <div class="hidden md:flex">
                    <a href="download.html" class="bg-primary-600 hover:bg-primary-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200">
                        Download Now
                    </a>
                </div>

                <!-- Mobile menu button -->
                <button class="md:hidden p-2" onclick="toggleMobileMenu()">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>

            <!-- Mobile Navigation -->
            <div id="mobile-menu" class="hidden md:hidden py-4 border-t border-gray-200">
                <nav class="flex flex-col space-y-4">
                    <a href="index.html" class="text-gray-600 hover:text-gray-900 font-medium">Home</a>
                    <a href="download.html" class="text-gray-600 hover:text-gray-900 font-medium">Download</a>
                    <a href="features.html" class="text-gray-600 hover:text-gray-900 font-medium">Features</a>
                    <a href="tutorials.html" class="text-gray-600 hover:text-gray-900 font-medium">Tutorials</a>
                    <a href="themes.html" class="text-gray-600 hover:text-gray-900 font-medium">Themes</a>
                    <a href="faq.html" class="text-gray-600 hover:text-gray-900 font-medium">FAQ</a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="py-16 lg:py-24 bg-gradient-to-br from-green-50 to-blue-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h1 class="text-4xl lg:text-6xl font-bold text-gray-900 leading-tight mb-6">
                    Better Canvas Changelog
                </h1>
                <p class="text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
                    Stay up-to-date with the latest Better Canvas extension updates, new features, better canvas themes,
                    bug fixes, and improvements. See what's new in each version release.
                </p>

                <!-- Version Stats -->
                <div class="flex flex-col sm:flex-row items-center justify-center space-y-2 sm:space-y-0 sm:space-x-8 mb-12">
                    <div class="flex items-center">
                        <span class="text-3xl font-bold text-green-600 mr-2">2.4.1</span>
                        <span class="text-gray-600 font-medium">Latest Version</span>
                    </div>
                    <div class="text-gray-600">
                        <span class="font-semibold">Regular</span> Updates
                    </div>
                    <div class="text-gray-600">
                        <span class="font-semibold">Active</span> Development
                    </div>
                </div>

                <!-- Update Notification -->
                <div class="bg-green-100 border border-green-200 rounded-lg p-6 max-w-2xl mx-auto mb-8">
                    <h3 class="font-semibold text-green-900 mb-2">🎉 Latest Update Available!</h3>
                    <p class="text-green-800 text-sm mb-4">
                        Version 2.4.1 is now available with new better canvas themes, improved performance, and bug fixes.
                    </p>
                    <div class="flex flex-col sm:flex-row gap-3 justify-center">
                        <a href="download.html" class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors text-sm">
                            Update Now
                        </a>
                        <a href="#latest" class="bg-white hover:bg-gray-50 text-green-600 font-medium py-2 px-4 rounded-lg border border-green-300 transition-colors text-sm">
                            View Changes
                        </a>
                    </div>
                </div>

                <!-- Filter Options -->
                <div class="flex flex-wrap justify-center gap-3">
                    <button onclick="filterVersions('all')" class="filter-btn bg-primary-600 text-white font-medium py-2 px-4 rounded-lg transition-colors text-sm">
                        All Updates
                    </button>
                    <button onclick="filterVersions('major')" class="filter-btn bg-white hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 rounded-lg border border-gray-300 transition-colors text-sm">
                        🚀 Major Releases
                    </button>
                    <button onclick="filterVersions('themes')" class="filter-btn bg-white hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 rounded-lg border border-gray-300 transition-colors text-sm">
                        🎨 Theme Updates
                    </button>
                    <button onclick="filterVersions('fixes')" class="filter-btn bg-white hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 rounded-lg border border-gray-300 transition-colors text-sm">
                        🐛 Bug Fixes
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Version History -->
    <section class="py-16 lg:py-24 bg-white">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                    Version History
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Complete changelog of Better Canvas extension updates, featuring new better canvas themes,
                    enhanced features, performance improvements, and bug fixes.
                </p>
            </div>

            <div class="relative">
                <div class="timeline-line"></div>

                <!-- Version 2.4.1 - Latest -->
                <div id="latest" class="version-card type-fixes type-themes mb-12 relative">
                    <div class="flex items-start">
                        <div class="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center mr-6 relative z-10">
                            <span class="text-white font-bold text-sm">2.4.1</span>
                        </div>
                        <div class="flex-1 bg-white rounded-lg border border-gray-200 shadow-sm p-6">
                            <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                                <div>
                                    <h3 class="text-xl font-semibold text-gray-900 mb-1">Version 2.4.1</h3>
                                    <p class="text-sm text-gray-500">Released December 15, 2024</p>
                                </div>
                                <div class="flex gap-2 mt-2 md:mt-0">
                                    <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-medium">Latest</span>
                                    <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium">Bug Fixes</span>
                                    <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded text-xs font-medium">Themes</span>
                                </div>
                            </div>

                            <div class="space-y-4">
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-2">🎨 Better Canvas Themes</h4>
                                    <ul class="text-sm text-gray-600 space-y-1 ml-4">
                                        <li>• Added 5 new better canvas themes: Ocean Blue, Forest Green, Sunset Orange, Purple Night, and Minimal Gray</li>
                                        <li>• Improved dark mode compatibility across all themes</li>
                                        <li>• Fixed theme switching issues in Firefox</li>
                                        <li>• Enhanced theme preview functionality</li>
                                    </ul>
                                </div>

                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-2">🐛 Bug Fixes</h4>
                                    <ul class="text-sm text-gray-600 space-y-1 ml-4">
                                        <li>• Fixed GPA calculator not updating in real-time</li>
                                        <li>• Resolved extension icon not showing on some Canvas pages</li>
                                        <li>• Fixed memory leak in theme engine</li>
                                        <li>• Corrected timezone issues in assignment reminders</li>
                                    </ul>
                                </div>

                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-2">⚡ Performance</h4>
                                    <ul class="text-sm text-gray-600 space-y-1 ml-4">
                                        <li>• Reduced extension loading time by 30%</li>
                                        <li>• Optimized theme switching performance</li>
                                        <li>• Improved Canvas page load speed</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Version 2.4.0 - Major Release -->
                <div class="version-card type-major type-themes mb-12 relative">
                    <div class="flex items-start">
                        <div class="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center mr-6 relative z-10">
                            <span class="text-white font-bold text-sm">2.4.0</span>
                        </div>
                        <div class="flex-1 bg-white rounded-lg border border-gray-200 shadow-sm p-6">
                            <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                                <div>
                                    <h3 class="text-xl font-semibold text-gray-900 mb-1">Version 2.4.0</h3>
                                    <p class="text-sm text-gray-500">Released November 28, 2024</p>
                                </div>
                                <div class="flex gap-2 mt-2 md:mt-0">
                                    <span class="bg-red-100 text-red-800 px-2 py-1 rounded text-xs font-medium">Major Release</span>
                                    <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded text-xs font-medium">Themes</span>
                                </div>
                            </div>

                            <div class="space-y-4">
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-2">🚀 New Features</h4>
                                    <ul class="text-sm text-gray-600 space-y-1 ml-4">
                                        <li>• Introduced Custom Theme Creator for personalized better canvas themes</li>
                                        <li>• Added Smart Study Planner with AI-powered recommendations</li>
                                        <li>• New Assignment Priority System with color coding</li>
                                        <li>• Enhanced GPA Calculator with semester planning</li>
                                    </ul>
                                </div>

                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-2">🎨 Theme System Overhaul</h4>
                                    <ul class="text-sm text-gray-600 space-y-1 ml-4">
                                        <li>• Completely redesigned theme engine for better performance</li>
                                        <li>• Added 15 new professional better canvas themes</li>
                                        <li>• Improved theme compatibility with Canvas updates</li>
                                        <li>• New theme sharing and import functionality</li>
                                    </ul>
                                </div>

                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-2">🔧 Improvements</h4>
                                    <ul class="text-sm text-gray-600 space-y-1 ml-4">
                                        <li>• Better Firefox compatibility and performance</li>
                                        <li>• Enhanced mobile Canvas support</li>
                                        <li>• Improved accessibility features</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Version 2.3.2 - Bug Fixes -->
                <div class="version-card type-fixes mb-12 relative">
                    <div class="flex items-start">
                        <div class="w-12 h-12 bg-orange-600 rounded-full flex items-center justify-center mr-6 relative z-10">
                            <span class="text-white font-bold text-sm">2.3.2</span>
                        </div>
                        <div class="flex-1 bg-white rounded-lg border border-gray-200 shadow-sm p-6">
                            <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                                <div>
                                    <h3 class="text-xl font-semibold text-gray-900 mb-1">Version 2.3.2</h3>
                                    <p class="text-sm text-gray-500">Released November 10, 2024</p>
                                </div>
                                <div class="flex gap-2 mt-2 md:mt-0">
                                    <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium">Bug Fixes</span>
                                </div>
                            </div>

                            <div class="space-y-4">
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-2">🐛 Critical Fixes</h4>
                                    <ul class="text-sm text-gray-600 space-y-1 ml-4">
                                        <li>• Fixed extension not loading on Canvas subdomains</li>
                                        <li>• Resolved better canvas themes not applying to new Canvas UI</li>
                                        <li>• Fixed notification permissions on Chrome 119+</li>
                                        <li>• Corrected grade calculation errors in some edge cases</li>
                                    </ul>
                                </div>

                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-2">🔧 Minor Improvements</h4>
                                    <ul class="text-sm text-gray-600 space-y-1 ml-4">
                                        <li>• Improved error handling and user feedback</li>
                                        <li>• Enhanced extension startup performance</li>
                                        <li>• Better Canvas API compatibility</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Version 2.3.1 - Theme Update -->
                <div class="version-card type-themes mb-12 relative">
                    <div class="flex items-start">
                        <div class="w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center mr-6 relative z-10">
                            <span class="text-white font-bold text-sm">2.3.1</span>
                        </div>
                        <div class="flex-1 bg-white rounded-lg border border-gray-200 shadow-sm p-6">
                            <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                                <div>
                                    <h3 class="text-xl font-semibold text-gray-900 mb-1">Version 2.3.1</h3>
                                    <p class="text-sm text-gray-500">Released October 25, 2024</p>
                                </div>
                                <div class="flex gap-2 mt-2 md:mt-0">
                                    <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded text-xs font-medium">Themes</span>
                                </div>
                            </div>

                            <div class="space-y-4">
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-2">🎨 New Better Canvas Themes</h4>
                                    <ul class="text-sm text-gray-600 space-y-1 ml-4">
                                        <li>• Added Halloween Special Theme (limited time)</li>
                                        <li>• New Academic Professional theme for formal presentations</li>
                                        <li>• Retro Canvas theme with vintage styling</li>
                                        <li>• High Contrast theme for accessibility</li>
                                    </ul>
                                </div>

                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-2">🔧 Theme Improvements</h4>
                                    <ul class="text-sm text-gray-600 space-y-1 ml-4">
                                        <li>• Better theme preview in settings</li>
                                        <li>• Improved theme loading speed</li>
                                        <li>• Fixed theme conflicts with Canvas updates</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Version 2.3.0 - Major Release -->
                <div class="version-card type-major mb-12 relative">
                    <div class="flex items-start">
                        <div class="w-12 h-12 bg-indigo-600 rounded-full flex items-center justify-center mr-6 relative z-10">
                            <span class="text-white font-bold text-sm">2.3.0</span>
                        </div>
                        <div class="flex-1 bg-white rounded-lg border border-gray-200 shadow-sm p-6">
                            <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                                <div>
                                    <h3 class="text-xl font-semibold text-gray-900 mb-1">Version 2.3.0</h3>
                                    <p class="text-sm text-gray-500">Released October 5, 2024</p>
                                </div>
                                <div class="flex gap-2 mt-2 md:mt-0">
                                    <span class="bg-red-100 text-red-800 px-2 py-1 rounded text-xs font-medium">Major Release</span>
                                </div>
                            </div>

                            <div class="space-y-4">
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-2">🚀 Major Features</h4>
                                    <ul class="text-sm text-gray-600 space-y-1 ml-4">
                                        <li>• Introduced Enhanced Todo System with categories and priorities</li>
                                        <li>• New Smart Notifications with customizable timing</li>
                                        <li>• Added Course Analytics and Progress Tracking</li>
                                        <li>• Integrated Calendar View with assignment deadlines</li>
                                    </ul>
                                </div>

                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-2">🎯 User Experience</h4>
                                    <ul class="text-sm text-gray-600 space-y-1 ml-4">
                                        <li>• Redesigned settings panel with better organization</li>
                                        <li>• Improved onboarding experience for new users</li>
                                        <li>• Enhanced keyboard shortcuts and accessibility</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Load More Button -->
            <div class="text-center mt-12">
                <button onclick="loadMoreVersions()" class="bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-3 px-6 rounded-lg transition-colors duration-200">
                    Load Earlier Versions
                </button>
            </div>
        </div>
    </section>

    <!-- Update Notification Section -->
    <section class="py-16 lg:py-24 bg-gray-50">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                Stay Updated with Better Canvas
            </h2>
            <p class="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
                Get the latest Better Canvas extension updates automatically and never miss new better canvas themes,
                features, or important bug fixes.
            </p>

            <div class="grid md:grid-cols-3 gap-8 mb-12">
                <!-- Auto Updates -->
                <div class="bg-white rounded-lg p-6 border border-gray-200 shadow-sm">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Automatic Updates</h3>
                    <p class="text-gray-600 text-sm">
                        Enable automatic updates in your browser to get the latest Better Canvas features as soon as they're released.
                    </p>
                </div>

                <!-- Release Notes -->
                <div class="bg-white rounded-lg p-6 border border-gray-200 shadow-sm">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Release Notes</h3>
                    <p class="text-gray-600 text-sm">
                        Check this changelog regularly to learn about new better canvas themes, features, and improvements.
                    </p>
                </div>

                <!-- Community -->
                <div class="bg-white rounded-lg p-6 border border-gray-200 shadow-sm">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a2 2 0 01-2-2v-6a2 2 0 012-2h8z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Join Community</h3>
                    <p class="text-gray-600 text-sm">
                        Follow our GitHub repository to get notified about updates and participate in discussions.
                    </p>
                </div>
            </div>

            <!-- Update CTA -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h4 class="font-semibold text-blue-900 mb-2">📢 Update Available</h4>
                <p class="text-blue-800 text-sm mb-4">
                    Make sure you're running the latest version of Better Canvas extension to enjoy all the new features and improvements.
                </p>
                <div class="flex flex-col sm:flex-row gap-3 justify-center">
                    <a href="download.html" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded-lg transition-colors">
                        Check for Updates
                    </a>
                    <a href="https://github.com/UseBetterCanvas/bettercanvas" target="_blank" rel="noopener noreferrer" class="bg-white hover:bg-gray-50 text-blue-600 font-medium py-2 px-6 rounded-lg border border-blue-300 transition-colors">
                        Follow on GitHub
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid md:grid-cols-4 gap-8">
                <!-- Brand -->
                <div class="md:col-span-1">
                    <div class="flex items-center space-x-2 mb-4">
                        <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-sm">BC</span>
                        </div>
                        <span class="text-xl font-bold">Better Canvas</span>
                    </div>
                    <p class="text-gray-400 text-sm leading-relaxed">
                        Transform your Canvas learning experience with Better Canvas extension - featuring dark mode, better canvas themes, GPA calculator, and productivity tools.
                    </p>
                </div>

                <!-- Quick Links -->
                <div>
                    <h3 class="font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-2 text-sm">
                        <li><a href="download.html" class="text-gray-400 hover:text-white transition-colors">Download</a></li>
                        <li><a href="features.html" class="text-gray-400 hover:text-white transition-colors">Features</a></li>
                        <li><a href="themes.html" class="text-gray-400 hover:text-white transition-colors">Themes</a></li>
                        <li><a href="tutorials.html" class="text-gray-400 hover:text-white transition-colors">Tutorials</a></li>
                    </ul>
                </div>

                <!-- Support -->
                <div>
                    <h3 class="font-semibold mb-4">Support</h3>
                    <ul class="space-y-2 text-sm">
                        <li><a href="faq.html" class="text-gray-400 hover:text-white transition-colors">FAQ</a></li>
                        <li><a href="troubleshooting.html" class="text-gray-400 hover:text-white transition-colors">Troubleshooting</a></li>
                        <li><a href="contact.html" class="text-gray-400 hover:text-white transition-colors">Contact Us</a></li>
                        <li><a href="changelog.html" class="text-gray-400 hover:text-white transition-colors">Changelog</a></li>
                    </ul>
                </div>

                <!-- Official Links -->
                <div>
                    <h3 class="font-semibold mb-4">Official Links</h3>
                    <ul class="space-y-2 text-sm">
                        <li><a href="https://chrome.google.com/webstore/detail/better-canvas/cndibmoanboadcifjkjbdpjgfedanolh" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition-colors">Chrome Store</a></li>
                        <li><a href="https://addons.mozilla.org/en-US/firefox/addon/better-canvas/" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition-colors">Firefox Add-ons</a></li>
                        <li><a href="https://github.com/UseBetterCanvas/bettercanvas" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition-colors">GitHub</a></li>
                        <li><a href="https://diditupe.dev/bettercanvas/" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition-colors">Official Site</a></li>
                    </ul>
                </div>
            </div>

            <!-- Bottom Bar -->
            <div class="border-t border-gray-800 mt-12 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <div class="text-sm text-gray-400 mb-4 md:mb-0">
                        © 2024 Better Canvas Resources. All rights reserved.
                    </div>
                    <div class="flex space-x-6 text-sm">
                        <a href="privacy.html" class="text-gray-400 hover:text-white transition-colors">Privacy Policy</a>
                        <a href="terms.html" class="text-gray-400 hover:text-white transition-colors">Terms of Use</a>
                        <a href="disclaimer.html" class="text-gray-400 hover:text-white transition-colors">Disclaimer</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script>
        function toggleMobileMenu() {
            const menu = document.getElementById('mobile-menu');
            menu.classList.toggle('hidden');
        }

        function filterVersions(type) {
            // Update active filter button
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('bg-primary-600', 'text-white');
                btn.classList.add('bg-white', 'text-gray-700', 'border', 'border-gray-300');
            });

            event.target.classList.remove('bg-white', 'text-gray-700', 'border', 'border-gray-300');
            event.target.classList.add('bg-primary-600', 'text-white');

            // Filter version cards
            const cards = document.querySelectorAll('.version-card');
            cards.forEach(card => {
                if (type === 'all') {
                    card.style.display = 'block';
                } else {
                    const hasType = card.classList.contains(`type-${type}`);
                    card.style.display = hasType ? 'block' : 'none';
                }
            });
        }

        function loadMoreVersions() {
            // This would typically load more versions via AJAX
            alert('This would load earlier versions of Better Canvas extension. In a real implementation, this would fetch more changelog data.');
        }

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
