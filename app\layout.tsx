import type { Metadata } from 'next'
import './globals.css'

export const metadata: Metadata = {
  title: 'BetterCanvas - Dark Mode, Themes & Tools for Canvas LMS | Free Download',
  description: 'Transform your Canvas experience with dark mode, custom themes, GPA calculator, and productivity tools. Free Chrome & Firefox extension with 100,000+ users.',
  keywords: 'BetterCanvas, Canvas extension, Canvas dark mode, Canvas themes, GPA calculator, Canvas tools',
  authors: [{ name: 'BetterCanvas Hub' }],
  viewport: 'width=device-width, initial-scale=1',
  robots: 'index, follow',
  openGraph: {
    title: 'BetterCanvas - Transform Your Canvas Learning Experience',
    description: 'Get dark mode, custom themes, GPA calculator, and powerful productivity tools for Canvas LMS',
    type: 'website',
    locale: 'en_US',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'BetterCanvas - Transform Your Canvas Learning Experience',
    description: 'Get dark mode, custom themes, GPA calculator, and powerful productivity tools for Canvas LMS',
  }
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="canonical" href="https://bettercanvas-hub.com" />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "SoftwareApplication",
              "name": "BetterCanvas",
              "description": "Browser extension that enhances Canvas LMS with dark mode, themes, and productivity tools",
              "applicationCategory": "BrowserApplication",
              "operatingSystem": "Chrome, Firefox",
              "offers": {
                "@type": "Offer",
                "price": "0",
                "priceCurrency": "USD"
              },
              "aggregateRating": {
                "@type": "AggregateRating",
                "ratingValue": "4.8",
                "ratingCount": "1250"
              }
            })
          }}
        />
      </head>
      <body className="antialiased">
        {children}
      </body>
    </html>
  )
}
