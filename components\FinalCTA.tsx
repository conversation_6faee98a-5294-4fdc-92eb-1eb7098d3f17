import Link from 'next/link'

export default function FinalCTA() {
  return (
    <section className="section-padding gradient-bg">
      <div className="container-custom">
        <div className="text-center text-white">
          <h2 className="text-3xl lg:text-4xl font-bold mb-4">
            Ready to Transform Your Canvas?
          </h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Join 100,000+ students who've already upgraded their Canvas experience with dark mode, themes, and productivity tools.
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
            <Link 
              href="https://chrome.google.com/webstore/detail/better-canvas/cndibmoanboadcifjkjbdpjgfedanolh"
              target="_blank"
              rel="noopener noreferrer"
              className="bg-white text-gray-900 hover:bg-gray-100 font-semibold py-4 px-8 rounded-lg transition-colors duration-200 inline-flex items-center justify-center text-lg"
            >
              <svg className="w-6 h-6 mr-3" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.042-3.441.219-.937 1.404-5.965 1.404-5.965s-.359-.719-.359-1.782c0-1.668.967-2.914 2.171-2.914 1.023 0 1.518.769 1.518 1.69 0 1.029-.655 2.568-.994 3.995-.283 1.194.599 2.169 1.777 2.169 2.133 0 3.772-2.249 3.772-5.495 0-2.873-2.064-4.882-5.012-4.882-3.414 0-5.418 2.561-5.418 5.207 0 1.031.397 2.138.893 2.738.098.119.112.224.083.345l-.333 1.36c-.053.22-.174.267-.402.161-1.499-.698-2.436-2.888-2.436-4.649 0-3.785 2.75-7.262 7.929-7.262 4.163 0 7.398 2.967 7.398 6.931 0 4.136-2.607 7.464-6.227 7.464-1.216 0-2.357-.631-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001 12.017.001z"/>
              </svg>
              Download for Chrome
            </Link>
            <Link 
              href="https://addons.mozilla.org/en-US/firefox/addon/better-canvas/"
              target="_blank"
              rel="noopener noreferrer"
              className="bg-transparent border-2 border-white text-white hover:bg-white hover:text-gray-900 font-semibold py-4 px-8 rounded-lg transition-colors duration-200 inline-flex items-center justify-center text-lg"
            >
              <svg className="w-6 h-6 mr-3" viewBox="0 0 24 24" fill="currentColor">
                <path d="M23.05 11.03c-.111-.313-.277-.62-.484-.906-.426-.588-.96-1.091-1.572-1.479-.612-.388-1.29-.66-1.996-.8-.706-.14-1.43-.148-2.14-.024-.71.124-1.395.372-2.02.73-.625.358-1.18.826-1.635 1.378-.455.552-.8 1.178-1.016 1.844-.216.666-.302 1.362-.254 2.052.048.69.238 1.364.56 1.984.322.62.774 1.175 1.33 1.634.556.459 1.205.812 1.912 1.04.707.228 1.46.33 2.216.3.756-.03 1.496-.192 2.178-.477.682-.285 1.295-.7 1.804-1.222.509-.522.904-1.14 1.164-1.82.26-.68.382-1.408.36-2.14-.022-.732-.166-1.452-.457-2.12z"/>
              </svg>
              Download for Firefox
            </Link>
          </div>

          {/* Trust Indicators */}
          <div className="flex flex-col sm:flex-row items-center justify-center space-y-2 sm:space-y-0 sm:space-x-8 text-sm opacity-90">
            <div className="flex items-center">
              <svg className="w-4 h-4 mr-2 text-green-300" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
              </svg>
              Safe & Secure
            </div>
            <div className="flex items-center">
              <svg className="w-4 h-4 mr-2 text-green-300" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              No Personal Data Required
            </div>
            <div className="flex items-center">
              <svg className="w-4 h-4 mr-2 text-green-300" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              100% Free Forever
            </div>
          </div>

          {/* Support Link */}
          <div className="mt-8 pt-8 border-t border-white border-opacity-20">
            <p className="text-sm opacity-75">
              Need help getting started?{' '}
              <Link href="/tutorials" className="underline hover:no-underline">
                Check our tutorials
              </Link>
              {' '}or{' '}
              <Link href="/contact" className="underline hover:no-underline">
                contact support
              </Link>
            </p>
          </div>
        </div>
      </div>
    </section>
  )
}
