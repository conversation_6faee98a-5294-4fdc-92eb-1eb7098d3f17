#!/usr/bin/env python3
"""
Simple HTTP server for Better Canvas website with Clean URL support
"""

import http.server
import socketserver
import os
import urllib.parse

class CleanURLHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        # Parse the URL
        parsed_path = urllib.parse.urlparse(self.path)
        path = parsed_path.path
        
        # Remove leading slash
        if path.startswith('/'):
            path = path[1:]
        
        # Handle root path
        if path == '' or path == '/':
            path = 'index.html'
        
        # Handle clean URLs - add .html extension if file doesn't exist
        elif not path.endswith('.html') and not '.' in path:
            # Check if the clean URL corresponds to an HTML file
            html_file = path + '.html'
            if os.path.exists(html_file):
                path = html_file
        
        # Update the path
        self.path = '/' + path
        
        # Call the parent method
        return super().do_GET()

def start_server(port=8000):
    """Start the HTTP server"""
    try:
        with socketserver.TCPServer(("", port), CleanURLHandler) as httpd:
            print(f"🌐 Better Canvas server starting...")
            print(f"📍 Server running at: http://localhost:{port}")
            print(f"🏠 Homepage: http://localhost:{port}/")
            print(f"📥 Download: http://localhost:{port}/download")
            print(f"⚙️  Features: http://localhost:{port}/features")
            print(f"🎨 Themes: http://localhost:{port}/themes")
            print(f"📚 Tutorials: http://localhost:{port}/tutorials")
            print(f"❓ FAQ: http://localhost:{port}/faq")
            print(f"\n🛑 Press Ctrl+C to stop the server")
            print("-" * 50)
            
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Server stopped")
    except OSError as e:
        if "Address already in use" in str(e):
            print(f"❌ Port {port} is already in use. Trying port {port + 1}...")
            start_server(port + 1)
        else:
            print(f"❌ Error starting server: {e}")

if __name__ == "__main__":
    start_server(8000)
