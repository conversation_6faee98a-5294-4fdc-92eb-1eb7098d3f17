'use client'

import { useState } from 'react'
import Link from 'next/link'

export default function FAQPreview() {
  const [openIndex, setOpenIndex] = useState<number | null>(0)

  const faqs = [
    {
      question: "Is BetterCanvas free to use?",
      answer: "Yes, BetterCanvas is completely free with no hidden costs or premium features. All functionality is available to every user."
    },
    {
      question: "Does it work with my school's Canvas?",
      answer: "BetterCanvas works with any Canvas LMS installation. Just make sure you're using Canvas, not other platforms like D2L or Moodle."
    },
    {
      question: "Will it slow down my browser?",
      answer: "No, BetterCanvas is optimized for performance and won't impact your browsing speed. The extension is lightweight and efficient."
    },
    {
      question: "Can I create my own themes?",
      answer: "Absolutely! BetterCanvas includes a theme creator, and you can share your themes with the community through our theme gallery."
    }
  ]

  return (
    <section className="section-padding bg-gray-50">
      <div className="container-custom">
        <div className="text-center mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            Frequently Asked Questions
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Get quick answers to the most common questions about BetterCanvas.
          </p>
        </div>

        <div className="max-w-3xl mx-auto">
          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-200">
                <button
                  className="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 transition-colors"
                  onClick={() => setOpenIndex(openIndex === index ? null : index)}
                >
                  <span className="font-semibold text-gray-900">{faq.question}</span>
                  <svg
                    className={`w-5 h-5 text-gray-500 transition-transform ${
                      openIndex === index ? 'rotate-180' : ''
                    }`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
                {openIndex === index && (
                  <div className="px-6 pb-4">
                    <p className="text-gray-600 leading-relaxed">{faq.answer}</p>
                  </div>
                )}
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <p className="text-gray-600 mb-6">
              Still have questions? We're here to help!
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/faq" className="btn-secondary">
                View All Questions
              </Link>
              <Link href="/contact" className="btn-primary">
                Contact Support
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
