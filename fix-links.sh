#!/bin/bash

# Script to fix internal links in HTML files from absolute paths to relative paths

echo "🔧 Fixing internal links in HTML files..."

# Array of HTML files
files=(
    "index.html"
    "download.html"
    "features.html"
    "tutorials.html"
    "themes.html"
    "faq.html"
    "troubleshooting.html"
    "contact.html"
    "changelog.html"
    "privacy.html"
    "terms.html"
    "disclaimer.html"
)

# Function to replace links in a file
fix_links_in_file() {
    local file=$1
    echo "Processing $file..."

    # Create backup
    cp "$file" "$file.backup"

    # Replace internal links (including anchors)
    sed -i 's|href="/"|href="index.html"|g' "$file"
    sed -i 's|href="/download"|href="download.html"|g' "$file"
    sed -i 's|href="/features#|href="features.html#|g' "$file"
    sed -i 's|href="/features"|href="features.html"|g' "$file"
    sed -i 's|href="/tutorials#|href="tutorials.html#|g' "$file"
    sed -i 's|href="/tutorials"|href="tutorials.html"|g' "$file"
    sed -i 's|href="/themes#|href="themes.html#|g' "$file"
    sed -i 's|href="/themes"|href="themes.html"|g' "$file"
    sed -i 's|href="/faq#|href="faq.html#|g' "$file"
    sed -i 's|href="/faq"|href="faq.html"|g' "$file"
    sed -i 's|href="/troubleshooting#|href="troubleshooting.html#|g' "$file"
    sed -i 's|href="/troubleshooting"|href="troubleshooting.html"|g' "$file"
    sed -i 's|href="/contact#|href="contact.html#|g' "$file"
    sed -i 's|href="/contact"|href="contact.html"|g' "$file"
    sed -i 's|href="/changelog#|href="changelog.html#|g' "$file"
    sed -i 's|href="/changelog"|href="changelog.html"|g' "$file"
    sed -i 's|href="/privacy#|href="privacy.html#|g' "$file"
    sed -i 's|href="/privacy"|href="privacy.html"|g' "$file"
    sed -i 's|href="/terms#|href="terms.html#|g' "$file"
    sed -i 's|href="/terms"|href="terms.html"|g' "$file"
    sed -i 's|href="/disclaimer#|href="disclaimer.html#|g' "$file"
    sed -i 's|href="/disclaimer"|href="disclaimer.html"|g' "$file"

    echo "✅ Fixed links in $file"
}

# Process each file
for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        fix_links_in_file "$file"
    else
        echo "⚠️  File $file not found, skipping..."
    fi
done

echo ""
echo "🎉 Link fixing complete!"
echo "📝 Backup files created with .backup extension"
echo "🌐 You can now test the links locally by opening index.html in your browser"
