# BetterCanvas Hub

A third-party resource website for the BetterCanvas browser extension, featuring downloads, tutorials, themes, and community support.

## About BetterCanvas

BetterCanvas is a browser extension that enhances Canvas LMS with:
- Dark mode and custom themes
- GPA calculator
- Enhanced todo list
- Card customization
- Assignment reminders
- And much more!

## This Website

This is an unofficial third-party resource site that provides:
- Easy access to download links
- Comprehensive tutorials
- Theme gallery
- FAQ and troubleshooting guides
- Latest updates and changelog

## Tech Stack

- **Framework**: Next.js 14 with App Router
- **Styling**: Tailwind CSS
- **Deployment**: Vercel (static export)
- **Language**: TypeScript

## Getting Started

### Prerequisites
- Node.js 18+ 
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd bettercanvas-hub
```

2. Install dependencies:
```bash
npm install
```

3. Run the development server:
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

### Building for Production

```bash
npm run build
```

This will create a static export in the `out/` directory, ready for deployment to Vercel or any static hosting service.

## Project Structure

```
├── app/                    # Next.js app directory
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Homepage
├── components/            # React components
│   ├── Header.tsx
│   ├── HeroSection.tsx
│   ├── FeaturesOverview.tsx
│   └── ...
├── public/               # Static assets
└── ...
```

## SEO Features

- Optimized meta tags and Open Graph
- Structured data (JSON-LD)
- Semantic HTML structure
- Fast loading with static generation
- Mobile-responsive design

## Legal Notice

This is an unofficial third-party website. BetterCanvas is developed by the original creators. This site is for informational purposes and provides easy access to official download links and community resources.

## Contributing

Contributions are welcome! Please feel free to submit issues or pull requests.

## License

This website code is open source. Please respect the original BetterCanvas project's licensing terms.
