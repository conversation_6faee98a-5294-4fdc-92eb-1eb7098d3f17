<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Better Canvas Support - Help & Community Resources</title>
    <meta name="description" content="Get Better Canvas support through GitHub issues, community forums, and help resources. Find assistance with installation, themes, features, and troubleshooting.">

    <!-- Open Graph -->
    <meta property="og:title" content="Contact Better Canvas Support - Get Help & Send Feedback">
    <meta property="og:description" content="Get help with Better Canvas extension. Contact our support team for installation help, theme issues, and general assistance.">
    <meta property="og:type" content="website">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                        }
                    }
                }
            }
        }
    </script>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        body { font-family: 'Inter', system-ui, sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .contact-card { transition: all 0.3s ease; }
        .contact-card:hover { transform: translateY(-2px); }
    </style>

    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "ContactPage",
        "name": "Better Canvas Contact",
        "description": "Contact Better Canvas support team for help with the extension",
        "about": {
            "@type": "SoftwareApplication",
            "name": "Better Canvas",
            "applicationCategory": "BrowserApplication"
        },
        "contactPoint": [
            {
                "@type": "ContactPoint",
                "contactType": "Customer Support",
                "availableLanguage": "English"
            }
        ]
    }
    </script>
</head>
<body class="antialiased">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="index.html" class="flex items-center space-x-2">
                        <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-sm">BC</span>
                        </div>
                        <span class="text-xl font-bold text-gray-900">Better Canvas</span>
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <nav class="hidden md:flex items-center space-x-8">
                    <a href="index.html" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">Home</a>
                    <a href="download.html" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">Download</a>
                    <a href="features.html" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">Features</a>
                    <a href="tutorials.html" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">Tutorials</a>
                    <a href="themes.html" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">Themes</a>
                    <a href="faq.html" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">FAQ</a>
                </nav>

                <!-- CTA Button -->
                <div class="hidden md:flex">
                    <a href="download.html" class="bg-primary-600 hover:bg-primary-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200">
                        Download Now
                    </a>
                </div>

                <!-- Mobile menu button -->
                <button class="md:hidden p-2" onclick="toggleMobileMenu()">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>

            <!-- Mobile Navigation -->
            <div id="mobile-menu" class="hidden md:hidden py-4 border-t border-gray-200">
                <nav class="flex flex-col space-y-4">
                    <a href="index.html" class="text-gray-600 hover:text-gray-900 font-medium">Home</a>
                    <a href="download.html" class="text-gray-600 hover:text-gray-900 font-medium">Download</a>
                    <a href="features.html" class="text-gray-600 hover:text-gray-900 font-medium">Features</a>
                    <a href="tutorials.html" class="text-gray-600 hover:text-gray-900 font-medium">Tutorials</a>
                    <a href="themes.html" class="text-gray-600 hover:text-gray-900 font-medium">Themes</a>
                    <a href="faq.html" class="text-gray-600 hover:text-gray-900 font-medium">FAQ</a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="py-16 lg:py-24 bg-gradient-to-br from-blue-50 to-indigo-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h1 class="text-4xl lg:text-6xl font-bold text-gray-900 leading-tight mb-6">
                    Better Canvas Support
                </h1>
                <p class="text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
                    Need help with Better Canvas extension? Find support through our community forums,
                    GitHub issues, and comprehensive self-help resources.
                </p>

                <!-- Response Time -->
                <div class="flex flex-col sm:flex-row items-center justify-center space-y-2 sm:space-y-0 sm:space-x-8 mb-12">
                    <div class="flex items-center">
                        <span class="text-3xl font-bold text-blue-600 mr-2">24h</span>
                        <span class="text-gray-600 font-medium">Average Response Time</span>
                    </div>
                    <div class="text-gray-600">
                        <span class="font-semibold">Multiple</span> Contact Methods
                    </div>
                    <div class="text-gray-600">
                        <span class="font-semibold">Expert</span> Support Team
                    </div>
                </div>

                <!-- Quick Help -->
                <div class="bg-blue-100 border border-blue-200 rounded-lg p-6 max-w-2xl mx-auto mb-8">
                    <h3 class="font-semibold text-blue-900 mb-2">🚀 Need Quick Help?</h3>
                    <p class="text-blue-800 text-sm mb-4">
                        Before contacting support, check our FAQ and troubleshooting guides for instant solutions.
                    </p>
                    <div class="flex flex-col sm:flex-row gap-3 justify-center">
                        <a href="faq.html" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors text-sm">
                            Check FAQ
                        </a>
                        <a href="troubleshooting.html" class="bg-white hover:bg-gray-50 text-blue-600 font-medium py-2 px-4 rounded-lg border border-blue-300 transition-colors text-sm">
                            Troubleshooting Guide
                        </a>
                    </div>
                </div>

                <!-- Contact Methods -->
                <div class="flex flex-wrap justify-center gap-3">
                    <a href="#community" class="bg-white hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 rounded-lg border border-gray-300 transition-colors text-sm">
                        💬 Community
                    </a>
                    <a href="#bug-report" class="bg-white hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 rounded-lg border border-gray-300 transition-colors text-sm">
                        🐛 Bug Report
                    </a>
                    <a href="#feedback" class="bg-white hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 rounded-lg border border-gray-300 transition-colors text-sm">
                        💡 Feedback
                    </a>
                </div>
            </div>
        </div>
    </section>



    <!-- Alternative Contact Methods -->
    <section id="community" class="py-16 lg:py-24 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                    Other Ways to Get Help
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Prefer a different way to connect? Choose from our various support channels and community resources.
                </p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Community Forum -->
                <div class="contact-card bg-white rounded-xl shadow-lg border border-gray-200 p-6 text-center">
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a2 2 0 01-2-2v-6a2 2 0 012-2h8z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">Community Forum</h3>
                    <p class="text-gray-600 text-sm mb-6">
                        Join our GitHub Discussions to ask questions, share tips, and connect with other Better Canvas users.
                    </p>
                    <div class="space-y-2 mb-6">
                        <div class="flex items-center justify-center text-sm text-gray-500">
                            <span class="mr-2">👥</span>
                            <span>Community Support</span>
                        </div>
                        <div class="flex items-center justify-center text-sm text-gray-500">
                            <span class="mr-2">🚀</span>
                            <span>Quick Responses</span>
                        </div>
                        <div class="flex items-center justify-center text-sm text-gray-500">
                            <span class="mr-2">💡</span>
                            <span>User Tips & Tricks</span>
                        </div>
                    </div>
                    <a href="https://github.com/UseBetterCanvas/bettercanvas/discussions" target="_blank" rel="noopener noreferrer"
                        class="bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200 inline-block">
                        Join Discussion
                    </a>
                </div>

                <!-- Bug Reports -->
                <div id="bug-report" class="contact-card bg-white rounded-xl shadow-lg border border-gray-200 p-6 text-center">
                    <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">Report a Bug</h3>
                    <p class="text-gray-600 text-sm mb-6">
                        Found a bug in Better Canvas extension? Help us improve by reporting issues on GitHub.
                    </p>
                    <div class="space-y-2 mb-6">
                        <div class="flex items-center justify-center text-sm text-gray-500">
                            <span class="mr-2">🐛</span>
                            <span>Bug Tracking</span>
                        </div>
                        <div class="flex items-center justify-center text-sm text-gray-500">
                            <span class="mr-2">🔧</span>
                            <span>Developer Response</span>
                        </div>
                        <div class="flex items-center justify-center text-sm text-gray-500">
                            <span class="mr-2">📈</span>
                            <span>Help Improve BC</span>
                        </div>
                    </div>
                    <a href="https://github.com/UseBetterCanvas/bettercanvas/issues" target="_blank" rel="noopener noreferrer"
                        class="bg-red-600 hover:bg-red-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200 inline-block">
                        Report Issue
                    </a>
                </div>

                <!-- Feature Requests -->
                <div id="feedback" class="contact-card bg-white rounded-xl shadow-lg border border-gray-200 p-6 text-center">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">Feature Requests</h3>
                    <p class="text-gray-600 text-sm mb-6">
                        Have an idea for a new feature or improvement? Share your suggestions with our development team.
                    </p>
                    <div class="space-y-2 mb-6">
                        <div class="flex items-center justify-center text-sm text-gray-500">
                            <span class="mr-2">💡</span>
                            <span>New Ideas</span>
                        </div>
                        <div class="flex items-center justify-center text-sm text-gray-500">
                            <span class="mr-2">🗳️</span>
                            <span>Community Voting</span>
                        </div>
                        <div class="flex items-center justify-center text-sm text-gray-500">
                            <span class="mr-2">🚀</span>
                            <span>Future Updates</span>
                        </div>
                    </div>
                    <a href="https://github.com/UseBetterCanvas/bettercanvas/discussions/categories/ideas" target="_blank" rel="noopener noreferrer"
                        class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200 inline-block">
                        Share Ideas
                    </a>
                </div>
            </div>

            <!-- Self-Help Resources -->
            <div class="mt-16">
                <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-8">
                    <div class="text-center mb-8">
                        <h3 class="text-2xl font-bold text-gray-900 mb-4">Self-Help Resources</h3>
                        <p class="text-gray-600">
                            Before contacting support, try these resources for quick answers to common questions.
                        </p>
                    </div>

                    <div class="grid md:grid-cols-4 gap-6">
                        <!-- FAQ -->
                        <div class="text-center">
                            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                                <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <h4 class="font-semibold text-gray-900 mb-2">FAQ</h4>
                            <p class="text-sm text-gray-600 mb-3">Common questions and answers</p>
                            <a href="faq.html" class="text-purple-600 hover:text-purple-700 font-medium text-sm">
                                View FAQ →
                            </a>
                        </div>

                        <!-- Troubleshooting -->
                        <div class="text-center">
                            <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                                <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                            </div>
                            <h4 class="font-semibold text-gray-900 mb-2">Troubleshooting</h4>
                            <p class="text-sm text-gray-600 mb-3">Fix common issues</p>
                            <a href="troubleshooting.html" class="text-orange-600 hover:text-orange-700 font-medium text-sm">
                                Get Help →
                            </a>
                        </div>

                        <!-- Tutorials -->
                        <div class="text-center">
                            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                                <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                </svg>
                            </div>
                            <h4 class="font-semibold text-gray-900 mb-2">Tutorials</h4>
                            <p class="text-sm text-gray-600 mb-3">Step-by-step guides</p>
                            <a href="tutorials.html" class="text-green-600 hover:text-green-700 font-medium text-sm">
                                Learn More →
                            </a>
                        </div>

                        <!-- Features -->
                        <div class="text-center">
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                </svg>
                            </div>
                            <h4 class="font-semibold text-gray-900 mb-2">Features</h4>
                            <p class="text-sm text-gray-600 mb-3">Explore all features</p>
                            <a href="features.html" class="text-blue-600 hover:text-blue-700 font-medium text-sm">
                                Discover →
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid md:grid-cols-4 gap-8">
                <!-- Brand -->
                <div class="md:col-span-1">
                    <div class="flex items-center space-x-2 mb-4">
                        <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-sm">BC</span>
                        </div>
                        <span class="text-xl font-bold">Better Canvas</span>
                    </div>
                    <p class="text-gray-400 text-sm leading-relaxed">
                        Transform your Canvas learning experience with Better Canvas extension - featuring dark mode, better canvas themes, GPA calculator, and productivity tools.
                    </p>
                </div>

                <!-- Quick Links -->
                <div>
                    <h3 class="font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-2 text-sm">
                        <li><a href="download.html" class="text-gray-400 hover:text-white transition-colors">Download</a></li>
                        <li><a href="features.html" class="text-gray-400 hover:text-white transition-colors">Features</a></li>
                        <li><a href="themes.html" class="text-gray-400 hover:text-white transition-colors">Themes</a></li>
                        <li><a href="tutorials.html" class="text-gray-400 hover:text-white transition-colors">Tutorials</a></li>
                    </ul>
                </div>

                <!-- Support -->
                <div>
                    <h3 class="font-semibold mb-4">Support</h3>
                    <ul class="space-y-2 text-sm">
                        <li><a href="faq.html" class="text-gray-400 hover:text-white transition-colors">FAQ</a></li>
                        <li><a href="troubleshooting.html" class="text-gray-400 hover:text-white transition-colors">Troubleshooting</a></li>
                        <li><a href="contact.html" class="text-gray-400 hover:text-white transition-colors">Contact Us</a></li>
                        <li><a href="changelog.html" class="text-gray-400 hover:text-white transition-colors">Changelog</a></li>
                    </ul>
                </div>

                <!-- Official Links -->
                <div>
                    <h3 class="font-semibold mb-4">Official Links</h3>
                    <ul class="space-y-2 text-sm">
                        <li><a href="https://chrome.google.com/webstore/detail/better-canvas/cndibmoanboadcifjkjbdpjgfedanolh" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition-colors">Chrome Store</a></li>
                        <li><a href="https://addons.mozilla.org/en-US/firefox/addon/better-canvas/" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition-colors">Firefox Add-ons</a></li>
                        <li><a href="https://github.com/UseBetterCanvas/bettercanvas" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition-colors">GitHub</a></li>
                        <li><a href="https://diditupe.dev/bettercanvas/" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition-colors">Official Site</a></li>
                    </ul>
                </div>
            </div>

            <!-- Bottom Bar -->
            <div class="border-t border-gray-800 mt-12 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <div class="text-sm text-gray-400 mb-4 md:mb-0">
                        © 2024 Better Canvas Resources. All rights reserved.
                    </div>
                    <div class="flex space-x-6 text-sm">
                        <a href="privacy.html" class="text-gray-400 hover:text-white transition-colors">Privacy Policy</a>
                        <a href="terms.html" class="text-gray-400 hover:text-white transition-colors">Terms of Use</a>
                        <a href="disclaimer.html" class="text-gray-400 hover:text-white transition-colors">Disclaimer</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script>
        function toggleMobileMenu() {
            const menu = document.getElementById('mobile-menu');
            menu.classList.toggle('hidden');
        }



        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
