'use client'

import { useState } from 'react'
import Link from 'next/link'

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  return (
    <header className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
      <div className="container-custom">
        <div className="flex justify-between items-center py-4">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">BC</span>
              </div>
              <span className="text-xl font-bold text-gray-900">BetterCanvas Hub</span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link href="/download" className="text-gray-600 hover:text-gray-900 font-medium transition-colors">
              Download
            </Link>
            <Link href="/features" className="text-gray-600 hover:text-gray-900 font-medium transition-colors">
              Features
            </Link>
            <Link href="/tutorials" className="text-gray-600 hover:text-gray-900 font-medium transition-colors">
              Tutorials
            </Link>
            <Link href="/themes" className="text-gray-600 hover:text-gray-900 font-medium transition-colors">
              Themes
            </Link>
            <Link href="/faq" className="text-gray-600 hover:text-gray-900 font-medium transition-colors">
              FAQ
            </Link>
            <Link href="/blog" className="text-gray-600 hover:text-gray-900 font-medium transition-colors">
              Blog
            </Link>
          </nav>

          {/* CTA Button */}
          <div className="hidden md:flex">
            <Link href="/download" className="btn-primary">
              Download Now
            </Link>
          </div>

          {/* Mobile menu button */}
          <button
            className="md:hidden p-2"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            aria-label="Toggle menu"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              {isMenuOpen ? (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              ) : (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              )}
            </svg>
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-gray-200">
            <nav className="flex flex-col space-y-4">
              <Link href="/download" className="text-gray-600 hover:text-gray-900 font-medium">
                Download
              </Link>
              <Link href="/features" className="text-gray-600 hover:text-gray-900 font-medium">
                Features
              </Link>
              <Link href="/tutorials" className="text-gray-600 hover:text-gray-900 font-medium">
                Tutorials
              </Link>
              <Link href="/themes" className="text-gray-600 hover:text-gray-900 font-medium">
                Themes
              </Link>
              <Link href="/faq" className="text-gray-600 hover:text-gray-900 font-medium">
                FAQ
              </Link>
              <Link href="/blog" className="text-gray-600 hover:text-gray-900 font-medium">
                Blog
              </Link>
              <Link href="/download" className="btn-primary w-full mt-4">
                Download Now
              </Link>
            </nav>
          </div>
        )}
      </div>
    </header>
  )
}
