<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Better Canvas - Canvas LMS Enhancement Extension</title>
    <meta name="description" content="Transform your Canvas LMS experience with Better Canvas extension. Get dark mode, 50+ custom themes, GPA calculator, and productivity tools for Chrome and Firefox.">

    <!-- Open Graph -->
    <meta property="og:title" content="Better Canvas - Transform Your Canvas Learning Experience">
    <meta property="og:description" content="Get dark mode, better canvas themes, GPA calculator, and powerful productivity tools with Better Canvas extension">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://www.better-canvas.com/">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                        }
                    }
                }
            }
        }
    </script>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        body { font-family: 'Inter', system-ui, sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .animate-fade-in { animation: fadeIn 0.5s ease-in-out; }
        .animate-slide-up { animation: slideUp 0.6s ease-out; }
        @keyframes fadeIn { from { opacity: 0; } to { opacity: 1; } }
        @keyframes slideUp { from { transform: translateY(20px); opacity: 0; } to { transform: translateY(0); opacity: 1; } }
    </style>

    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-7KZLFW1P0V"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'G-7KZLFW1P0V');
    </script>

    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "SoftwareApplication",
        "name": "Better Canvas",
        "description": "Better Canvas extension that enhances Canvas LMS with dark mode, better canvas themes, and productivity tools",
        "applicationCategory": "BrowserApplication",
        "operatingSystem": "Chrome, Firefox",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
        },
        "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": "4.8",
            "ratingCount": "1250"
        }
    }
    </script>
</head>
<body class="antialiased">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="/" class="flex items-center space-x-2">
                        <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-sm">BC</span>
                        </div>
                        <span class="text-xl font-bold text-gray-900">Better Canvas</span>
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <nav class="hidden md:flex items-center space-x-8">
                    <a href="download" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">Download</a>
                    <a href="features" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">Features</a>
                    <a href="tutorials" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">Tutorials</a>
                    <a href="themes" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">Themes</a>
                    <a href="faq" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">FAQ</a>
                </nav>

                <!-- CTA Button -->
                <div class="hidden md:flex">
                    <a href="download" class="bg-primary-600 hover:bg-primary-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200">
                        Download Now
                    </a>
                </div>

                <!-- Mobile menu button -->
                <button class="md:hidden p-2" onclick="toggleMobileMenu()">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>

            <!-- Mobile Navigation -->
            <div id="mobile-menu" class="hidden md:hidden py-4 border-t border-gray-200">
                <nav class="flex flex-col space-y-4">
                    <a href="download" class="text-gray-600 hover:text-gray-900 font-medium">Download</a>
                    <a href="features" class="text-gray-600 hover:text-gray-900 font-medium">Features</a>
                    <a href="tutorials" class="text-gray-600 hover:text-gray-900 font-medium">Tutorials</a>
                    <a href="themes" class="text-gray-600 hover:text-gray-900 font-medium">Themes</a>
                    <a href="faq" class="text-gray-600 hover:text-gray-900 font-medium">FAQ</a>
                    <a href="download" class="bg-primary-600 hover:bg-primary-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200 text-center mt-4">
                        Download Now
                    </a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="py-16 lg:py-24 bg-gradient-to-br from-gray-50 to-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid lg:grid-cols-2 gap-12 items-center">
                <!-- Content -->
                <div class="animate-fade-in">
                    <h1 class="text-4xl lg:text-6xl font-bold text-gray-900 leading-tight mb-4">
                        Better Canvas
                    </h1>
                    <h2 class="text-2xl lg:text-3xl font-semibold text-gray-700 mb-6 leading-tight">
                        Transform Your Canvas with Better Canvas Extension
                    </h2>
                    <p class="text-xl text-gray-600 mb-8 leading-relaxed">
                        Get dark mode, better canvas themes, GPA calculator, and powerful productivity tools for Canvas LMS.
                        Join 100,000+ students who've already upgraded their Canvas experience with Better Canvas.
                    </p>

                    <!-- Trust Indicators -->
                    <div class="flex items-center space-x-6 mb-8">
                        <div class="flex items-center">
                            <div class="flex text-yellow-400">
                                <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                                <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                                <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                                <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                                <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                            </div>
                            <span class="ml-2 text-gray-600 font-medium">4.8/5 rating</span>
                        </div>
                        <div class="text-gray-600">
                            <span class="font-semibold">100,000+</span> users
                        </div>
                    </div>

                    <!-- CTA Buttons -->
                    <div class="flex flex-col sm:flex-row gap-4">
                        <a href="https://chrome.google.com/webstore/detail/better-canvas/cndibmoanboadcifjkjbdpjgfedanolh" target="_blank" rel="noopener noreferrer" class="bg-primary-600 hover:bg-primary-700 text-white font-semibold py-4 px-8 rounded-lg transition-colors duration-200 inline-flex items-center justify-center text-lg">
                            <img src="https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v9/icons/googlechrome.svg" alt="Chrome" class="w-6 h-6 mr-3 filter brightness-0 invert">
                            Better Canvas Chrome Extension
                        </a>
                        <a href="https://addons.mozilla.org/en-US/firefox/addon/better-canvas/" target="_blank" rel="noopener noreferrer" class="bg-white hover:bg-gray-50 text-gray-900 font-semibold py-4 px-8 rounded-lg border border-gray-300 transition-colors duration-200 inline-flex items-center justify-center text-lg">
                            <img src="https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v9/icons/firefox.svg" alt="Firefox" class="w-6 h-6 mr-3">
                            Better Canvas Firefox
                        </a>
                    </div>

                    <!-- Security Note -->
                    <p class="text-sm text-gray-500 mt-4 flex items-center">
                        <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                        </svg>
                        Safe & Secure • No Personal Data Required
                    </p>
                </div>

                <!-- Hero Image -->
                <div class="animate-slide-up">
                    <div class="relative">
                        <!-- Better Canvas Screenshot -->
                        <div class="bg-white rounded-2xl shadow-2xl p-4">
                            <img
                                src="images/main01.png"
                                alt="Better Canvas Main Interface - Dark Mode Dashboard with Custom Themes"
                                class="w-full h-auto rounded-lg shadow-lg"
                                loading="lazy"
                                onerror="this.style.display='none'; this.nextElementSibling.style.display='block';"
                            >
                            <!-- Fallback content if image fails to load -->
                            <div class="hidden bg-gray-900 rounded-lg p-6 text-white">
                                <div class="flex items-center space-x-2 mb-4">
                                    <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                                    <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                                    <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                                </div>
                                <div class="space-y-3">
                                    <div class="h-4 bg-gray-700 rounded w-3/4"></div>
                                    <div class="h-4 bg-gray-700 rounded w-1/2"></div>
                                    <div class="grid grid-cols-2 gap-4 mt-6">
                                        <div class="bg-blue-600 rounded-lg p-4 h-20"></div>
                                        <div class="bg-purple-600 rounded-lg p-4 h-20"></div>
                                        <div class="bg-green-600 rounded-lg p-4 h-20"></div>
                                        <div class="bg-orange-600 rounded-lg p-4 h-20"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Floating elements -->
                        <div class="absolute -top-4 -right-4 bg-primary-600 text-white px-3 py-1 rounded-full text-sm font-semibold shadow-lg">
                            Dark Mode
                        </div>
                        <div class="absolute -bottom-4 -left-4 bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold shadow-lg">
                            GPA: 3.8
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Social Proof Section -->
    <section class="py-12 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Stats -->
            <div class="grid grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
                <div class="text-center">
                    <div class="text-3xl lg:text-4xl font-bold text-primary-600 mb-2">100,000+</div>
                    <div class="text-gray-600 font-medium">Active Users</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl lg:text-4xl font-bold text-primary-600 mb-2">4.8/5</div>
                    <div class="text-gray-600 font-medium">User Rating</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl lg:text-4xl font-bold text-primary-600 mb-2">50+</div>
                    <div class="text-gray-600 font-medium">Available Themes</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl lg:text-4xl font-bold text-primary-600 mb-2">2</div>
                    <div class="text-gray-600 font-medium">Supported Browsers</div>
                </div>
            </div>

            <!-- Testimonials -->
            <div class="grid md:grid-cols-3 gap-8">
                <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                    <div class="flex text-yellow-400 mb-4">
                        <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                        <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                        <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                        <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                        <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                    </div>
                    <p class="text-gray-700 mb-4 italic">"BetterCanvas made my late-night studying so much easier with dark mode!"</p>
                    <div class="text-sm">
                        <div class="font-semibold text-gray-900">Sarah</div>
                        <div class="text-gray-500">College Student</div>
                    </div>
                </div>
                <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                    <div class="flex text-yellow-400 mb-4">
                        <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                        <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                        <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                        <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                        <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                    </div>
                    <p class="text-gray-700 mb-4 italic">"The GPA calculator is a game-changer. I always know where I stand."</p>
                    <div class="text-sm">
                        <div class="font-semibold text-gray-900">Mike</div>
                        <div class="text-gray-500">University Student</div>
                    </div>
                </div>
                <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                    <div class="flex text-yellow-400 mb-4">
                        <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                        <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                        <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                        <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                        <svg class="w-5 h-5 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                    </div>
                    <p class="text-gray-700 mb-4 italic">"Love the custom themes! My Canvas looks amazing now."</p>
                    <div class="text-sm">
                        <div class="font-semibold text-gray-900">Jessica</div>
                        <div class="text-gray-500">Graduate Student</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Overview Section -->
    <section class="py-16 lg:py-24 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                    Better Canvas Features for Enhanced Learning
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Better Canvas extension transforms your Canvas experience with powerful tools designed to boost productivity and make studying more enjoyable.
                </p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Feature 1: Dark Mode -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200 group">
                    <div class="text-4xl mb-4">🌙</div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">Beautiful Dark Mode</h3>
                    <p class="text-gray-600 mb-4 leading-relaxed">
                        Easy on the eyes with fully customizable dark themes. Perfect for late-night studying sessions.
                    </p>
                    <a href="features#dark-mode" class="text-primary-600 hover:text-primary-700 font-medium inline-flex items-center group-hover:translate-x-1 transition-transform duration-200">
                        Learn More
                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                </div>

                <!-- Feature 2: Themes -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200 group">
                    <div class="text-4xl mb-4">🎨</div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">50+ Better Canvas Themes</h3>
                    <p class="text-gray-600 mb-4 leading-relaxed">
                        Personalize your Canvas with beautiful better canvas themes created by the community.
                    </p>
                    <a href="themes" class="text-primary-600 hover:text-primary-700 font-medium inline-flex items-center group-hover:translate-x-1 transition-transform duration-200">
                        Learn More
                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                </div>

                <!-- Feature 3: GPA Calculator -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200 group">
                    <div class="text-4xl mb-4">📊</div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">Smart GPA Calculator</h3>
                    <p class="text-gray-600 mb-4 leading-relaxed">
                        Track your grades and calculate what-if scenarios to stay on top of your academic goals.
                    </p>
                    <a href="features#gpa-calculator" class="text-primary-600 hover:text-primary-700 font-medium inline-flex items-center group-hover:translate-x-1 transition-transform duration-200">
                        Learn More
                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                </div>

                <!-- Feature 4: Todo List -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200 group">
                    <div class="text-4xl mb-4">✅</div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">Enhanced Todo List</h3>
                    <p class="text-gray-600 mb-4 leading-relaxed">
                        Never miss an assignment with our improved todo list and assignment tracking.
                    </p>
                    <a href="features#todo-list" class="text-primary-600 hover:text-primary-700 font-medium inline-flex items-center group-hover:translate-x-1 transition-transform duration-200">
                        Learn More
                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                </div>

                <!-- Feature 5: Card Customization -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200 group">
                    <div class="text-4xl mb-4">🎯</div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">Card Customization</h3>
                    <p class="text-gray-600 mb-4 leading-relaxed">
                        Customize your course cards with colors, images, and layouts that match your style.
                    </p>
                    <a href="features#card-customization" class="text-primary-600 hover:text-primary-700 font-medium inline-flex items-center group-hover:translate-x-1 transition-transform duration-200">
                        Learn More
                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                </div>

                <!-- Feature 6: Reminders -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200 group">
                    <div class="text-4xl mb-4">🔔</div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">Smart Reminders</h3>
                    <p class="text-gray-600 mb-4 leading-relaxed">
                        Get browser-wide popup reminders for upcoming assignments and deadlines.
                    </p>
                    <a href="features#reminders" class="text-primary-600 hover:text-primary-700 font-medium inline-flex items-center group-hover:translate-x-1 transition-transform duration-200">
                        Learn More
                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                </div>
            </div>

            <!-- CTA -->
            <div class="text-center mt-12">
                <a href="features" class="bg-primary-600 hover:bg-primary-700 text-white font-semibold py-4 px-8 rounded-lg transition-colors duration-200 text-lg">
                    View All Features
                </a>
            </div>
        </div>
    </section>

    <!-- How It Works Section -->
    <section class="py-16 lg:py-24 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                    Install Better Canvas Extension in 3 Simple Steps
                </h2>
                <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                    Transform your Canvas experience with Better Canvas in less than 2 minutes. No technical knowledge required.
                </p>
            </div>

            <div class="grid lg:grid-cols-3 gap-8 lg:gap-12">
                <!-- Step 1 -->
                <div class="text-center">
                    <div class="relative mb-6">
                        <div class="w-20 h-20 bg-primary-600 rounded-full flex items-center justify-center text-3xl mx-auto mb-4">
                            ⬇️
                        </div>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">
                        1. Download Extension
                    </h3>
                    <p class="text-gray-600 leading-relaxed mb-6">
                        Install Better Canvas extension from Chrome Web Store or Firefox Add-ons in seconds.
                    </p>
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 h-64 flex items-center justify-center overflow-hidden">
                        <img
                            src="images/step01.png"
                            alt="Chrome Web Store - Better Canvas Extension Installation Page"
                            class="w-full h-full object-cover rounded cursor-pointer hover:scale-105 transition-transform duration-200"
                            loading="lazy"
                            onclick="openImageModal(this.src, this.alt)"
                            onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
                        >
                        <div class="hidden text-gray-400 text-sm text-center">
                            Browser extension installation interface
                            <br />
                            <span class="text-xs">[Screenshot placeholder]</span>
                        </div>
                    </div>
                </div>

                <!-- Step 2 -->
                <div class="text-center">
                    <div class="relative mb-6">
                        <div class="w-20 h-20 bg-primary-600 rounded-full flex items-center justify-center text-3xl mx-auto mb-4">
                            ⚙️
                        </div>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">
                        2. Quick Setup
                    </h3>
                    <p class="text-gray-600 leading-relaxed mb-6">
                        Open Canvas and configure your preferences. No account required.
                    </p>
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 h-64 flex items-center justify-center overflow-hidden">
                        <img
                            src="images/step02.png"
                            alt="Better Canvas Settings Configuration Panel"
                            class="w-full h-full object-cover rounded cursor-pointer hover:scale-105 transition-transform duration-200"
                            loading="lazy"
                            onclick="openImageModal(this.src, this.alt)"
                            onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
                        >
                        <div class="hidden text-gray-400 text-sm text-center">
                            Settings configuration panel
                            <br />
                            <span class="text-xs">[Screenshot placeholder]</span>
                        </div>
                    </div>
                </div>

                <!-- Step 3 -->
                <div class="text-center">
                    <div class="relative mb-6">
                        <div class="w-20 h-20 bg-primary-600 rounded-full flex items-center justify-center text-3xl mx-auto mb-4">
                            🎉
                        </div>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">
                        3. Transform Canvas
                    </h3>
                    <p class="text-gray-600 leading-relaxed mb-6">
                        Enjoy a beautiful, productive Canvas experience with all features enabled.
                    </p>
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 h-64 flex items-center justify-center overflow-hidden">
                        <img
                            src="images/step03.png"
                            alt="Enhanced Canvas Dashboard with Better Canvas Features"
                            class="w-full h-full object-cover rounded cursor-pointer hover:scale-105 transition-transform duration-200"
                            loading="lazy"
                            onclick="openImageModal(this.src, this.alt)"
                            onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
                        >
                        <div class="hidden text-gray-400 text-sm text-center">
                            Enhanced Canvas dashboard
                            <br />
                            <span class="text-xs">[Screenshot placeholder]</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional Info -->
            <div class="mt-16 text-center">
                <div class="bg-white rounded-xl p-8 shadow-sm border border-gray-200 max-w-2xl mx-auto">
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">
                        Need Help Getting Started?
                    </h3>
                    <p class="text-gray-600 mb-4">
                        Check out our detailed installation guide and video tutorials.
                    </p>
                    <div class="flex flex-col sm:flex-row gap-3 justify-center">
                        <a href="tutorials#installation" class="bg-white hover:bg-gray-50 text-gray-900 font-semibold py-3 px-6 rounded-lg border border-gray-300 transition-colors duration-200">
                            Installation Guide
                        </a>
                        <a href="tutorials" class="bg-primary-600 hover:bg-primary-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200">
                            Watch Tutorials
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Latest Updates Section -->
    <section class="py-16 lg:py-24 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid lg:grid-cols-2 gap-12 items-center">
                <!-- Content -->
                <div>
                    <div class="inline-flex items-center bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium mb-4">
                        <span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                        Latest Update
                    </div>

                    <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                        Version 5.10 Now Available
                    </h2>

                    <p class="text-xl text-gray-600 mb-6">
                        Released December 2024 with bug fixes, new themes, and performance improvements.
                    </p>

                    <div class="space-y-4 mb-8">
                        <div class="flex items-start space-x-3">
                            <span class="text-xl">🐛</span>
                            <span class="text-gray-700">Fixed dark mode bugs in discussion text boxes</span>
                        </div>
                        <div class="flex items-start space-x-3">
                            <span class="text-xl">🎨</span>
                            <span class="text-gray-700">Added new themes and fonts</span>
                        </div>
                        <div class="flex items-start space-x-3">
                            <span class="text-xl">⚡</span>
                            <span class="text-gray-700">Card colors now change instantly</span>
                        </div>
                        <div class="flex items-start space-x-3">
                            <span class="text-xl">🔧</span>
                            <span class="text-gray-700">Improved todo list functionality</span>
                        </div>
                    </div>

                    <div class="flex flex-col sm:flex-row gap-4">
                        <a href="changelog" class="bg-white hover:bg-gray-50 text-gray-900 font-semibold py-3 px-6 rounded-lg border border-gray-300 transition-colors duration-200">
                            View Full Changelog
                        </a>
                        <a href="https://chrome.google.com/webstore/detail/better-canvas/cndibmoanboadcifjkjbdpjgfedanolh" target="_blank" rel="noopener noreferrer" class="bg-primary-600 hover:bg-primary-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200">
                            Download Update
                        </a>
                    </div>
                </div>

                <!-- Visual -->
                <div class="relative">
                    <div class="bg-gradient-to-br from-primary-50 to-primary-100 rounded-2xl p-8">
                        <div class="bg-white rounded-xl shadow-lg p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="font-semibold text-gray-900">BetterCanvas v5.10</h3>
                                <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-medium">
                                    Updated
                                </span>
                            </div>

                            <div class="space-y-3">
                                <div class="flex items-center space-x-2">
                                    <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                                    <span class="text-sm text-gray-600">Dark mode improvements</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                                    <span class="text-sm text-gray-600">New theme collection</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <div class="w-3 h-3 bg-purple-500 rounded-full"></div>
                                    <span class="text-sm text-gray-600">Performance boost</span>
                                </div>
                            </div>

                            <div class="mt-6 pt-4 border-t border-gray-200">
                                <div class="flex justify-between text-sm text-gray-500">
                                    <span>Release Date</span>
                                    <span>Dec 2024</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Floating notification -->
                    <div class="absolute -top-4 -right-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-semibold animate-pulse">
                        New!
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Preview Section -->
    <section class="py-16 lg:py-24 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                    Frequently Asked Questions
                </h2>
                <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                    Get quick answers to the most common questions about BetterCanvas.
                </p>
            </div>

            <div class="max-w-3xl mx-auto">
                <div class="space-y-4">
                    <!-- FAQ 1 -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                        <button class="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 transition-colors" onclick="toggleFAQ(1)">
                            <span class="font-semibold text-gray-900">Is BetterCanvas free to use?</span>
                            <svg id="faq-icon-1" class="w-5 h-5 text-gray-500 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div id="faq-content-1" class="hidden px-6 pb-4">
                            <p class="text-gray-600 leading-relaxed">Yes, BetterCanvas is completely free with no hidden costs or premium features. All functionality is available to every user.</p>
                        </div>
                    </div>

                    <!-- FAQ 2 -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                        <button class="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 transition-colors" onclick="toggleFAQ(2)">
                            <span class="font-semibold text-gray-900">Does it work with my school's Canvas?</span>
                            <svg id="faq-icon-2" class="w-5 h-5 text-gray-500 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div id="faq-content-2" class="hidden px-6 pb-4">
                            <p class="text-gray-600 leading-relaxed">BetterCanvas works with any Canvas LMS installation. Just make sure you're using Canvas, not other platforms like D2L or Moodle.</p>
                        </div>
                    </div>

                    <!-- FAQ 3 -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                        <button class="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 transition-colors" onclick="toggleFAQ(3)">
                            <span class="font-semibold text-gray-900">Will it slow down my browser?</span>
                            <svg id="faq-icon-3" class="w-5 h-5 text-gray-500 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div id="faq-content-3" class="hidden px-6 pb-4">
                            <p class="text-gray-600 leading-relaxed">No, BetterCanvas is optimized for performance and won't impact your browsing speed. The extension is lightweight and efficient.</p>
                        </div>
                    </div>

                    <!-- FAQ 4 -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                        <button class="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 transition-colors" onclick="toggleFAQ(4)">
                            <span class="font-semibold text-gray-900">Can I create my own themes?</span>
                            <svg id="faq-icon-4" class="w-5 h-5 text-gray-500 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div id="faq-content-4" class="hidden px-6 pb-4">
                            <p class="text-gray-600 leading-relaxed">Absolutely! BetterCanvas includes a theme creator, and you can share your themes with the community through our theme gallery.</p>
                        </div>
                    </div>
                </div>

                <div class="text-center mt-12">
                    <p class="text-gray-600 mb-6">
                        Still have questions? We're here to help!
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <a href="faq" class="bg-white hover:bg-gray-50 text-gray-900 font-semibold py-3 px-6 rounded-lg border border-gray-300 transition-colors duration-200">
                            View All Questions
                        </a>
                        <a href="contact" class="bg-primary-600 hover:bg-primary-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200">
                            Contact Support
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Final CTA Section -->
    <section class="py-16 lg:py-24 gradient-bg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center text-white">
                <h2 class="text-3xl lg:text-4xl font-bold mb-4">
                    Ready to Transform Your Canvas with Better Canvas?
                </h2>
                <p class="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
                    Join 100,000+ students who've already upgraded their Canvas experience with Better Canvas extension - featuring dark mode, better canvas themes, and productivity tools.
                </p>

                <!-- CTA Buttons -->
                <div class="flex flex-col sm:flex-row gap-4 justify-center mb-8">
                    <a href="https://chrome.google.com/webstore/detail/better-canvas/cndibmoanboadcifjkjbdpjgfedanolh" target="_blank" rel="noopener noreferrer" class="bg-white text-gray-900 hover:bg-gray-100 font-semibold py-4 px-8 rounded-lg transition-colors duration-200 inline-flex items-center justify-center text-lg">
                        <img src="https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v9/icons/googlechrome.svg" alt="Chrome" class="w-6 h-6 mr-3">
                        Better Canvas Chrome Extension
                    </a>
                    <a href="https://addons.mozilla.org/en-US/firefox/addon/better-canvas/" target="_blank" rel="noopener noreferrer" class="bg-transparent border-2 border-white text-white hover:bg-white hover:text-gray-900 font-semibold py-4 px-8 rounded-lg transition-colors duration-200 inline-flex items-center justify-center text-lg">
                        <img src="https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v9/icons/firefox.svg" alt="Firefox" class="w-6 h-6 mr-3 filter brightness-0 invert">
                        Better Canvas Firefox
                    </a>
                </div>

                <!-- Trust Indicators -->
                <div class="flex flex-col sm:flex-row items-center justify-center space-y-2 sm:space-y-0 sm:space-x-8 text-sm opacity-90">
                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-2 text-green-300" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                        </svg>
                        Safe & Secure
                    </div>
                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-2 text-green-300" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        No Personal Data Required
                    </div>
                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-2 text-green-300" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        100% Free Forever
                    </div>
                </div>

                <!-- Support Link -->
                <div class="mt-8 pt-8 border-t border-white border-opacity-20">
                    <p class="text-sm opacity-75">
                        Need help getting started?{' '}
                        <a href="tutorials" class="underline hover:no-underline">
                            Check our tutorials
                        </a>
                        {' '}or{' '}
                        <a href="contact" class="underline hover:no-underline">
                            contact support
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid md:grid-cols-4 gap-8">
                <!-- Brand -->
                <div class="md:col-span-1">
                    <div class="flex items-center space-x-2 mb-4">
                        <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-sm">BC</span>
                        </div>
                        <span class="text-xl font-bold">Better Canvas</span>
                    </div>
                    <p class="text-gray-400 text-sm leading-relaxed">
                        Transform your Canvas learning experience with Better Canvas extension - featuring dark mode, better canvas themes, GPA calculator, and productivity tools.
                    </p>
                </div>

                <!-- Quick Links -->
                <div>
                    <h3 class="font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-2 text-sm">
                        <li><a href="download" class="text-gray-400 hover:text-white transition-colors">Download</a></li>
                        <li><a href="features" class="text-gray-400 hover:text-white transition-colors">Features</a></li>
                        <li><a href="themes" class="text-gray-400 hover:text-white transition-colors">Themes</a></li>
                        <li><a href="tutorials" class="text-gray-400 hover:text-white transition-colors">Tutorials</a></li>
                    </ul>
                </div>

                <!-- Support -->
                <div>
                    <h3 class="font-semibold mb-4">Support</h3>
                    <ul class="space-y-2 text-sm">
                        <li><a href="faq" class="text-gray-400 hover:text-white transition-colors">FAQ</a></li>
                        <li><a href="troubleshooting" class="text-gray-400 hover:text-white transition-colors">Troubleshooting</a></li>
                        <li><a href="contact" class="text-gray-400 hover:text-white transition-colors">Contact Us</a></li>
                        <li><a href="changelog" class="text-gray-400 hover:text-white transition-colors">Changelog</a></li>
                    </ul>
                </div>

                <!-- Official Links -->
                <div>
                    <h3 class="font-semibold mb-4">Official Links</h3>
                    <ul class="space-y-2 text-sm">
                        <li><a href="https://chrome.google.com/webstore/detail/better-canvas/cndibmoanboadcifjkjbdpjgfedanolh" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition-colors">Chrome Store</a></li>
                        <li><a href="https://addons.mozilla.org/en-US/firefox/addon/better-canvas/" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition-colors">Firefox Add-ons</a></li>
                        <li><a href="https://github.com/UseBetterCanvas/bettercanvas" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition-colors">GitHub</a></li>
                        <li><a href="https://diditupe.dev/bettercanvas/" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition-colors">Official Site</a></li>
                    </ul>
                </div>
            </div>

            <!-- Bottom Bar -->
            <div class="border-t border-gray-800 mt-12 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <div class="text-sm text-gray-400 mb-4 md:mb-0">
                        © 2024 Better Canvas Resources. All rights reserved.
                    </div>
                    <div class="flex space-x-6 text-sm">
                        <a href="privacy" class="text-gray-400 hover:text-white transition-colors">Privacy Policy</a>
                        <a href="terms" class="text-gray-400 hover:text-white transition-colors">Terms of Use</a>
                        <a href="disclaimer" class="text-gray-400 hover:text-white transition-colors">Disclaimer</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Image Modal -->
    <div id="imageModal" class="fixed inset-0 bg-black bg-opacity-75 z-50 hidden flex items-center justify-center p-4" onclick="closeImageModal()">
        <div class="relative max-w-4xl max-h-full">
            <img id="modalImage" src="" alt="" class="max-w-full max-h-full object-contain rounded-lg shadow-2xl">
            <button onclick="closeImageModal()" class="absolute top-4 right-4 text-white bg-black bg-opacity-50 hover:bg-opacity-75 rounded-full p-2 transition-colors duration-200">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
            <div id="modalCaption" class="absolute bottom-4 left-4 right-4 text-white bg-black bg-opacity-50 rounded p-2 text-sm"></div>
        </div>
    </div>

    <script>
        function toggleMobileMenu() {
            const menu = document.getElementById('mobile-menu');
            menu.classList.toggle('hidden');
        }

        function toggleFAQ(index) {
            const content = document.getElementById(`faq-content-${index}`);
            const icon = document.getElementById(`faq-icon-${index}`);

            if (content.classList.contains('hidden')) {
                content.classList.remove('hidden');
                icon.style.transform = 'rotate(180deg)';
            } else {
                content.classList.add('hidden');
                icon.style.transform = 'rotate(0deg)';
            }
        }

        // Image Modal Functions
        function openImageModal(src, alt) {
            const modal = document.getElementById('imageModal');
            const modalImage = document.getElementById('modalImage');
            const modalCaption = document.getElementById('modalCaption');

            modalImage.src = src;
            modalImage.alt = alt;
            modalCaption.textContent = alt;

            modal.classList.remove('hidden');
            document.body.style.overflow = 'hidden'; // Prevent background scrolling
        }

        function closeImageModal() {
            const modal = document.getElementById('imageModal');
            modal.classList.add('hidden');
            document.body.style.overflow = 'auto'; // Restore scrolling
        }

        // Close modal with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeImageModal();
            }
        });

        // Prevent modal from closing when clicking on the image
        document.getElementById('modalImage').addEventListener('click', function(event) {
            event.stopPropagation();
        });
    </script>
</body>
</html>
