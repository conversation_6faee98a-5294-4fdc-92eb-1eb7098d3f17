<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Better Canvas FAQ - Questions & Troubleshooting</title>
    <meta name="description" content="Find answers to common Better Canvas questions: installation help, theme setup, dark mode issues, GPA calculator usage, and troubleshooting guides for Chrome and Firefox.">

    <!-- Open Graph -->
    <meta property="og:title" content="Better Canvas FAQ - Common Questions & Answers">
    <meta property="og:description" content="Find answers to common questions about Better Canvas extension including installation, themes, features, and troubleshooting.">
    <meta property="og:type" content="website">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                        }
                    }
                }
            }
        }
    </script>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        body { font-family: 'Inter', system-ui, sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .faq-item { transition: all 0.3s ease; }
        .faq-answer { max-height: 0; overflow: hidden; transition: max-height 0.3s ease; }
        .faq-answer.open { max-height: 500px; }
    </style>

    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-7KZLFW1P0V"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'G-7KZLFW1P0V');
    </script>

    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "FAQPage",
        "mainEntity": [
            {
                "@type": "Question",
                "name": "What is Better Canvas extension?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "Better Canvas is a browser extension that enhances Canvas LMS with features like dark mode, better canvas themes, GPA calculator, enhanced todo lists, and productivity tools."
                }
            },
            {
                "@type": "Question",
                "name": "How do I install Better Canvas?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "You can install Better Canvas from Chrome Web Store for Chrome browsers or Firefox Add-ons for Firefox. Simply search for 'Better Canvas' and click install."
                }
            },
            {
                "@type": "Question",
                "name": "Are better canvas themes free?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "Yes, all better canvas themes are completely free to use. We offer 50+ themes including dark themes, colorful themes, and minimalist designs."
                }
            }
        ]
    }
    </script>
</head>
<body class="antialiased">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="index.html" class="flex items-center space-x-2">
                        <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-sm">BC</span>
                        </div>
                        <span class="text-xl font-bold text-gray-900">Better Canvas</span>
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <nav class="hidden md:flex items-center space-x-8">
                    <a href="index.html" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">Home</a>
                    <a href="download.html" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">Download</a>
                    <a href="features.html" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">Features</a>
                    <a href="tutorials.html" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">Tutorials</a>
                    <a href="themes.html" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">Themes</a>
                    <a href="faq.html" class="text-primary-600 font-medium">FAQ</a>
                </nav>

                <!-- CTA Button -->
                <div class="hidden md:flex">
                    <a href="download.html" class="bg-primary-600 hover:bg-primary-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200">
                        Download Now
                    </a>
                </div>

                <!-- Mobile menu button -->
                <button class="md:hidden p-2" onclick="toggleMobileMenu()">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>

            <!-- Mobile Navigation -->
            <div id="mobile-menu" class="hidden md:hidden py-4 border-t border-gray-200">
                <nav class="flex flex-col space-y-4">
                    <a href="index.html" class="text-gray-600 hover:text-gray-900 font-medium">Home</a>
                    <a href="download.html" class="text-gray-600 hover:text-gray-900 font-medium">Download</a>
                    <a href="features.html" class="text-gray-600 hover:text-gray-900 font-medium">Features</a>
                    <a href="tutorials.html" class="text-gray-600 hover:text-gray-900 font-medium">Tutorials</a>
                    <a href="themes.html" class="text-gray-600 hover:text-gray-900 font-medium">Themes</a>
                    <a href="faq.html" class="text-primary-600 font-medium">FAQ</a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="py-16 lg:py-24 bg-gradient-to-br from-blue-50 to-indigo-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h1 class="text-4xl lg:text-6xl font-bold text-gray-900 leading-tight mb-6">
                    Better Canvas FAQ
                </h1>
                <p class="text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
                    Find answers to common questions about Better Canvas extension, including installation help,
                    better canvas themes, features, troubleshooting, and more.
                </p>

                <!-- Search Box -->
                <div class="max-w-2xl mx-auto mb-12">
                    <div class="relative">
                        <input
                            type="text"
                            id="faq-search"
                            placeholder="Search for answers..."
                            class="w-full px-6 py-4 text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                            onkeyup="searchFAQ()"
                        >
                        <div class="absolute inset-y-0 right-0 flex items-center pr-6">
                            <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- Quick Stats -->
                <div class="flex flex-col sm:flex-row items-center justify-center space-y-2 sm:space-y-0 sm:space-x-8 mb-8">
                    <div class="flex items-center">
                        <span class="text-2xl font-bold text-blue-600 mr-2">25+</span>
                        <span class="text-gray-600 font-medium">Common Questions</span>
                    </div>
                    <div class="text-gray-600">
                        <span class="font-semibold">Instant</span> Answers
                    </div>
                    <div class="text-gray-600">
                        <span class="font-semibold">24/7</span> Available
                    </div>
                </div>

                <!-- Category Navigation -->
                <div class="flex flex-wrap justify-center gap-3">
                    <button onclick="filterFAQ('all')" class="category-btn active bg-blue-600 text-white font-medium py-2 px-4 rounded-lg transition-colors text-sm">
                        All Questions
                    </button>
                    <button onclick="filterFAQ('installation')" class="category-btn bg-white hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 rounded-lg border border-gray-300 transition-colors text-sm">
                        Installation
                    </button>
                    <button onclick="filterFAQ('themes')" class="category-btn bg-white hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 rounded-lg border border-gray-300 transition-colors text-sm">
                        Themes
                    </button>
                    <button onclick="filterFAQ('features')" class="category-btn bg-white hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 rounded-lg border border-gray-300 transition-colors text-sm">
                        Features
                    </button>
                    <button onclick="filterFAQ('troubleshooting')" class="category-btn bg-white hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 rounded-lg border border-gray-300 transition-colors text-sm">
                        Troubleshooting
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Content -->
    <section class="py-16 lg:py-24 bg-white">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="space-y-6" id="faq-container">

                <!-- Installation Questions -->
                <div class="faq-item bg-white rounded-lg border border-gray-200 shadow-sm" data-category="installation">
                    <button class="faq-question w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors" onclick="toggleFAQ(this)">
                        <span class="text-lg font-semibold text-gray-900">What is Better Canvas extension?</span>
                        <svg class="faq-icon w-5 h-5 text-gray-500 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div class="faq-answer px-6 pb-4">
                        <p class="text-gray-600 leading-relaxed">
                            Better Canvas is a free browser extension that enhances your Canvas LMS experience with powerful features like dark mode, 50+ better canvas themes, GPA calculator, enhanced todo lists, smart reminders, and productivity tools. It's designed to make studying more enjoyable and efficient for students and educators.
                        </p>
                    </div>
                </div>

                <div class="faq-item bg-white rounded-lg border border-gray-200 shadow-sm" data-category="installation">
                    <button class="faq-question w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors" onclick="toggleFAQ(this)">
                        <span class="text-lg font-semibold text-gray-900">How do I install Better Canvas extension?</span>
                        <svg class="faq-icon w-5 h-5 text-gray-500 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div class="faq-answer px-6 pb-4">
                        <p class="text-gray-600 leading-relaxed mb-3">
                            Installing Better Canvas is easy and takes less than 2 minutes:
                        </p>
                        <ol class="list-decimal list-inside text-gray-600 space-y-2">
                            <li><strong>For Chrome:</strong> Visit Chrome Web Store, search "Better Canvas", and click "Add to Chrome"</li>
                            <li><strong>For Firefox:</strong> Visit Firefox Add-ons, search "Better Canvas", and click "Add to Firefox"</li>
                            <li>The extension will automatically activate when you visit Canvas</li>
                            <li>Look for the Better Canvas icon in your browser toolbar to access settings</li>
                        </ol>
                    </div>
                </div>

                <div class="faq-item bg-white rounded-lg border border-gray-200 shadow-sm" data-category="installation">
                    <button class="faq-question w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors" onclick="toggleFAQ(this)">
                        <span class="text-lg font-semibold text-gray-900">Is Better Canvas extension safe to use?</span>
                        <svg class="faq-icon w-5 h-5 text-gray-500 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div class="faq-answer px-6 pb-4">
                        <p class="text-gray-600 leading-relaxed">
                            Yes, Better Canvas extension is completely safe. It's open source, verified by browser stores, and regularly scanned for malware. The extension doesn't collect personal data, doesn't access your grades or assignments, and only enhances the visual appearance and functionality of Canvas. Over 100,000+ students trust Better Canvas for their daily Canvas usage.
                        </p>
                    </div>
                </div>

                <!-- Themes Questions -->
                <div class="faq-item bg-white rounded-lg border border-gray-200 shadow-sm" data-category="themes">
                    <button class="faq-question w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors" onclick="toggleFAQ(this)">
                        <span class="text-lg font-semibold text-gray-900">Are better canvas themes free?</span>
                        <svg class="faq-icon w-5 h-5 text-gray-500 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div class="faq-answer px-6 pb-4">
                        <p class="text-gray-600 leading-relaxed">
                            Yes! All 50+ better canvas themes are completely free to use. We offer a wide variety including dark themes, colorful themes, minimalist designs, and seasonal themes. You can switch between themes anytime without any cost or limitations. New themes are regularly added to the collection based on community requests.
                        </p>
                    </div>
                </div>

                <div class="faq-item bg-white rounded-lg border border-gray-200 shadow-sm" data-category="themes">
                    <button class="faq-question w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors" onclick="toggleFAQ(this)">
                        <span class="text-lg font-semibold text-gray-900">How do I install and change better canvas themes?</span>
                        <svg class="faq-icon w-5 h-5 text-gray-500 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div class="faq-answer px-6 pb-4">
                        <p class="text-gray-600 leading-relaxed mb-3">
                            Changing better canvas themes is simple:
                        </p>
                        <ol class="list-decimal list-inside text-gray-600 space-y-2">
                            <li>Make sure Better Canvas extension is installed</li>
                            <li>Open Canvas in your browser</li>
                            <li>Click the Better Canvas icon in your browser toolbar</li>
                            <li>Navigate to the "Themes" section</li>
                            <li>Browse available themes and click "Apply" on your favorite</li>
                            <li>The theme will be applied instantly across all Canvas pages</li>
                        </ol>
                    </div>
                </div>

                <div class="faq-item bg-white rounded-lg border border-gray-200 shadow-sm" data-category="themes">
                    <button class="faq-question w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors" onclick="toggleFAQ(this)">
                        <span class="text-lg font-semibold text-gray-900">Can I create my own better canvas themes?</span>
                        <svg class="faq-icon w-5 h-5 text-gray-500 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div class="faq-answer px-6 pb-4">
                        <p class="text-gray-600 leading-relaxed">
                            Yes! Better Canvas includes a built-in theme creator that allows you to design custom themes. You can customize colors, fonts, layouts, and visual elements to create your perfect Canvas experience. Once created, you can save your theme, share it with friends, or even submit it to our community gallery for others to enjoy.
                        </p>
                    </div>
                </div>

                <!-- Features Questions -->
                <div class="faq-item bg-white rounded-lg border border-gray-200 shadow-sm" data-category="features">
                    <button class="faq-question w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors" onclick="toggleFAQ(this)">
                        <span class="text-lg font-semibold text-gray-900">What features does Better Canvas extension include?</span>
                        <svg class="faq-icon w-5 h-5 text-gray-500 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div class="faq-answer px-6 pb-4">
                        <p class="text-gray-600 leading-relaxed mb-3">
                            Better Canvas includes many powerful features:
                        </p>
                        <ul class="list-disc list-inside text-gray-600 space-y-1">
                            <li><strong>Dark Mode:</strong> Easy on the eyes for late-night studying</li>
                            <li><strong>50+ Themes:</strong> Beautiful better canvas themes for personalization</li>
                            <li><strong>GPA Calculator:</strong> Track your academic performance with what-if scenarios</li>
                            <li><strong>Enhanced Todo List:</strong> Better task management with priorities</li>
                            <li><strong>Smart Reminders:</strong> Never miss assignments or deadlines</li>
                            <li><strong>Card Customization:</strong> Personalize your dashboard layout</li>
                            <li><strong>Performance Optimization:</strong> Faster page loading and smoother navigation</li>
                        </ul>
                    </div>
                </div>

                <div class="faq-item bg-white rounded-lg border border-gray-200 shadow-sm" data-category="features">
                    <button class="faq-question w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors" onclick="toggleFAQ(this)">
                        <span class="text-lg font-semibold text-gray-900">How accurate is the GPA calculator?</span>
                        <svg class="faq-icon w-5 h-5 text-gray-500 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div class="faq-answer px-6 pb-4">
                        <p class="text-gray-600 leading-relaxed">
                            The Better Canvas GPA calculator is highly accurate and works with all standard grading systems. It automatically pulls your grades from Canvas and calculates your GPA in real-time. The calculator supports weighted grades, different grading scales (4.0, 5.0, etc.), and what-if scenarios to help you plan your academic goals. However, always verify important calculations with your academic advisor.
                        </p>
                    </div>
                <!-- Troubleshooting Questions -->
                <div class="faq-item bg-white rounded-lg border border-gray-200 shadow-sm" data-category="troubleshooting">
                    <button class="faq-question w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors" onclick="toggleFAQ(this)">
                        <span class="text-lg font-semibold text-gray-900">Better Canvas extension is not working. What should I do?</span>
                        <svg class="faq-icon w-5 h-5 text-gray-500 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div class="faq-answer px-6 pb-4">
                        <p class="text-gray-600 leading-relaxed mb-3">
                            Try these troubleshooting steps:
                        </p>
                        <ol class="list-decimal list-inside text-gray-600 space-y-2">
                            <li>Refresh your Canvas page (Ctrl+F5 or Cmd+Shift+R)</li>
                            <li>Check if the extension is enabled in your browser settings</li>
                            <li>Disable other Canvas-related extensions temporarily</li>
                            <li>Clear your browser cache and cookies</li>
                            <li>Try disabling and re-enabling Better Canvas extension</li>
                            <li>If issues persist, contact our support team</li>
                        </ol>
                    </div>
                </div>

                <div class="faq-item bg-white rounded-lg border border-gray-200 shadow-sm" data-category="troubleshooting">
                    <button class="faq-question w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors" onclick="toggleFAQ(this)">
                        <span class="text-lg font-semibold text-gray-900">Why can't I see better canvas themes?</span>
                        <svg class="faq-icon w-5 h-5 text-gray-500 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div class="faq-answer px-6 pb-4">
                        <p class="text-gray-600 leading-relaxed">
                            If you can't see better canvas themes, make sure: 1) Better Canvas extension is properly installed and enabled, 2) You're on a Canvas page (themes only work on Canvas LMS), 3) Your browser allows the extension to run on Canvas domains, 4) You've clicked the Better Canvas icon and navigated to the Themes section. If themes still don't appear, try refreshing the page or reinstalling the extension.
                        </p>
                    </div>
                </div>

                <div class="faq-item bg-white rounded-lg border border-gray-200 shadow-sm" data-category="troubleshooting">
                    <button class="faq-question w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors" onclick="toggleFAQ(this)">
                        <span class="text-lg font-semibold text-gray-900">Does Better Canvas work on mobile devices?</span>
                        <svg class="faq-icon w-5 h-5 text-gray-500 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div class="faq-answer px-6 pb-4">
                        <p class="text-gray-600 leading-relaxed">
                            Better Canvas extension works on mobile browsers that support extensions, such as Firefox Mobile and Kiwi Browser (Chrome-based). However, most mobile browsers don't support extensions, so Better Canvas is primarily designed for desktop use. For the best experience with better canvas themes and all features, we recommend using Chrome or Firefox on a computer.
                        </p>
                    </div>
                </div>

                <!-- General Questions -->
                <div class="faq-item bg-white rounded-lg border border-gray-200 shadow-sm" data-category="features">
                    <button class="faq-question w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors" onclick="toggleFAQ(this)">
                        <span class="text-lg font-semibold text-gray-900">Is Better Canvas extension free?</span>
                        <svg class="faq-icon w-5 h-5 text-gray-500 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div class="faq-answer px-6 pb-4">
                        <p class="text-gray-600 leading-relaxed">
                            Yes! Better Canvas extension is completely free to download and use. All features including dark mode, 50+ better canvas themes, GPA calculator, enhanced todo lists, and productivity tools are available at no cost. There are no premium features, subscriptions, or hidden fees. We believe in making Canvas better for all students and educators.
                        </p>
                    </div>
                </div>

                <div class="faq-item bg-white rounded-lg border border-gray-200 shadow-sm" data-category="features">
                    <button class="faq-question w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors" onclick="toggleFAQ(this)">
                        <span class="text-lg font-semibold text-gray-900">Will Better Canvas affect my Canvas grades or data?</span>
                        <svg class="faq-icon w-5 h-5 text-gray-500 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div class="faq-answer px-6 pb-4">
                        <p class="text-gray-600 leading-relaxed">
                            No, Better Canvas extension only enhances the visual appearance and user interface of Canvas. It doesn't modify, access, or interfere with your grades, assignments, submissions, or any academic data. The extension works purely on the frontend to improve your Canvas experience with better canvas themes, dark mode, and productivity tools while keeping all your data safe and unchanged.
                        </p>
                    </div>
                </div>

                <div class="faq-item bg-white rounded-lg border border-gray-200 shadow-sm" data-category="installation">
                    <button class="faq-question w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors" onclick="toggleFAQ(this)">
                        <span class="text-lg font-semibold text-gray-900">Can I use Better Canvas on multiple browsers?</span>
                        <svg class="faq-icon w-5 h-5 text-gray-500 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div class="faq-answer px-6 pb-4">
                        <p class="text-gray-600 leading-relaxed">
                            Yes! You can install Better Canvas extension on multiple browsers. It's available for both Chrome (and Chromium-based browsers) and Firefox. You'll need to install it separately on each browser, but your settings and preferred better canvas themes can be configured independently on each browser to match your preferences.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Support Section -->
    <section class="py-16 lg:py-24 bg-gray-50">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                Still Have Questions?
            </h2>
            <p class="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
                Can't find the answer you're looking for? Our support team is here to help with any Better Canvas extension questions.
            </p>

            <div class="grid md:grid-cols-3 gap-8 mb-12">
                <!-- Email Support -->
                <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
                    <div class="text-3xl mb-4">📧</div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Email Support</h3>
                    <p class="text-gray-600 text-sm mb-4">Get detailed help via email</p>
                    <a href="contact.html" class="text-primary-600 hover:text-primary-700 font-medium">
                        Contact Us
                    </a>
                </div>

                <!-- Documentation -->
                <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
                    <div class="text-3xl mb-4">📚</div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Documentation</h3>
                    <p class="text-gray-600 text-sm mb-4">Detailed guides and tutorials</p>
                    <a href="tutorials.html" class="text-primary-600 hover:text-primary-700 font-medium">
                        View Tutorials
                    </a>
                </div>

                <!-- Community -->
                <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
                    <div class="text-3xl mb-4">💬</div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Community</h3>
                    <p class="text-gray-600 text-sm mb-4">Connect with other users</p>
                    <a href="https://github.com/UseBetterCanvas/bettercanvas" target="_blank" rel="noopener noreferrer" class="text-primary-600 hover:text-primary-700 font-medium">
                        Join GitHub
                    </a>
                </div>
            </div>

            <!-- Quick Download CTA -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h4 class="font-semibold text-blue-900 mb-2">Ready to Try Better Canvas?</h4>
                <p class="text-blue-800 text-sm mb-4">
                    Download Better Canvas extension now and transform your Canvas experience with dark mode, better canvas themes, and productivity tools.
                </p>
                <div class="flex flex-col sm:flex-row gap-3 justify-center">
                    <a href="download.html" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded-lg transition-colors">
                        Download Now
                    </a>
                    <a href="features.html" class="bg-white hover:bg-gray-50 text-blue-600 font-medium py-2 px-6 rounded-lg border border-blue-300 transition-colors">
                        View Features
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid md:grid-cols-4 gap-8">
                <!-- Brand -->
                <div class="md:col-span-1">
                    <div class="flex items-center space-x-2 mb-4">
                        <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-sm">BC</span>
                        </div>
                        <span class="text-xl font-bold">Better Canvas</span>
                    </div>
                    <p class="text-gray-400 text-sm leading-relaxed">
                        Transform your Canvas learning experience with Better Canvas extension - featuring dark mode, better canvas themes, GPA calculator, and productivity tools.
                    </p>
                </div>

                <!-- Quick Links -->
                <div>
                    <h3 class="font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-2 text-sm">
                        <li><a href="download.html" class="text-gray-400 hover:text-white transition-colors">Download</a></li>
                        <li><a href="features.html" class="text-gray-400 hover:text-white transition-colors">Features</a></li>
                        <li><a href="themes.html" class="text-gray-400 hover:text-white transition-colors">Themes</a></li>
                        <li><a href="tutorials.html" class="text-gray-400 hover:text-white transition-colors">Tutorials</a></li>
                    </ul>
                </div>

                <!-- Support -->
                <div>
                    <h3 class="font-semibold mb-4">Support</h3>
                    <ul class="space-y-2 text-sm">
                        <li><a href="faq.html" class="text-gray-400 hover:text-white transition-colors">FAQ</a></li>
                        <li><a href="troubleshooting.html" class="text-gray-400 hover:text-white transition-colors">Troubleshooting</a></li>
                        <li><a href="contact.html" class="text-gray-400 hover:text-white transition-colors">Contact Us</a></li>
                        <li><a href="changelog.html" class="text-gray-400 hover:text-white transition-colors">Changelog</a></li>
                    </ul>
                </div>

                <!-- Official Links -->
                <div>
                    <h3 class="font-semibold mb-4">Official Links</h3>
                    <ul class="space-y-2 text-sm">
                        <li><a href="https://chrome.google.com/webstore/detail/better-canvas/cndibmoanboadcifjkjbdpjgfedanolh" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition-colors">Chrome Store</a></li>
                        <li><a href="https://addons.mozilla.org/en-US/firefox/addon/better-canvas/" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition-colors">Firefox Add-ons</a></li>
                        <li><a href="https://github.com/UseBetterCanvas/bettercanvas" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition-colors">GitHub</a></li>
                        <li><a href="https://diditupe.dev/bettercanvas/" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition-colors">Official Site</a></li>
                    </ul>
                </div>
            </div>

            <!-- Bottom Bar -->
            <div class="border-t border-gray-800 mt-12 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <div class="text-sm text-gray-400 mb-4 md:mb-0">
                        © 2024 Better Canvas Resources. All rights reserved.
                    </div>
                    <div class="flex space-x-6 text-sm">
                        <a href="privacy.html" class="text-gray-400 hover:text-white transition-colors">Privacy Policy</a>
                        <a href="terms.html" class="text-gray-400 hover:text-white transition-colors">Terms of Use</a>
                        <a href="disclaimer.html" class="text-gray-400 hover:text-white transition-colors">Disclaimer</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script>
        function toggleMobileMenu() {
            const menu = document.getElementById('mobile-menu');
            menu.classList.toggle('hidden');
        }

        function searchFAQ() {
            const searchTerm = document.getElementById('faq-search').value.toLowerCase();
            const faqItems = document.querySelectorAll('.faq-item');

            faqItems.forEach(item => {
                const question = item.querySelector('.faq-question').textContent.toLowerCase();
                const answer = item.querySelector('.faq-answer').textContent.toLowerCase();

                if (question.includes(searchTerm) || answer.includes(searchTerm)) {
                    item.style.display = 'block';
                } else {
                    item.style.display = searchTerm === '' ? 'block' : 'none';
                }
            });
        }

        function filterFAQ(category) {
            // Update active category button
            document.querySelectorAll('.category-btn').forEach(btn => {
                btn.classList.remove('active', 'bg-blue-600', 'text-white');
                btn.classList.add('bg-white', 'text-gray-700');
            });
            event.target.classList.add('active', 'bg-blue-600', 'text-white');
            event.target.classList.remove('bg-white', 'text-gray-700');

            // Filter FAQ items
            const faqItems = document.querySelectorAll('.faq-item');
            faqItems.forEach(item => {
                if (category === 'all' || item.dataset.category === category) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        function toggleFAQ(element) {
            const answer = element.nextElementSibling;
            const icon = element.querySelector('.faq-icon');

            answer.classList.toggle('open');
            icon.style.transform = answer.classList.contains('open') ? 'rotate(180deg)' : 'rotate(0deg)';
        }
    </script>
</body>
</html>
