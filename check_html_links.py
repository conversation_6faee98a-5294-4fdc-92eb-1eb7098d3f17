#!/usr/bin/env python3
"""
Script to check for remaining .html links in HTML files
"""

import os
import re
import glob

def check_html_links():
    """Check all HTML files for remaining .html links"""
    html_files = glob.glob("*.html")
    
    # Internal pages that should use clean URLs
    internal_pages = [
        'index.html', 'download.html', 'features.html', 'themes.html',
        'tutorials.html', 'faq.html', 'contact.html', 'troubleshooting.html',
        'changelog.html', 'privacy.html', 'terms.html', 'disclaimer.html',
        'thank-you.html'
    ]
    
    issues_found = {}
    
    for html_file in html_files:
        if not os.path.exists(html_file):
            continue
            
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        file_issues = []
        
        # Check for href links to internal pages
        for page in internal_pages:
            # Look for href="page.html" patterns
            pattern = f'href="{page}"'
            matches = re.findall(pattern, content)
            if matches:
                file_issues.append(f"Found {len(matches)} instances of href=\"{page}\"")
        
        # Check for og:url with .html
        og_url_pattern = r'<meta property="og:url" content="[^"]*\.html">'
        og_matches = re.findall(og_url_pattern, content)
        if og_matches:
            file_issues.append(f"Found {len(og_matches)} og:url tags with .html extension")
        
        if file_issues:
            issues_found[html_file] = file_issues
    
    return issues_found

def main():
    """Main function"""
    print("🔍 Checking for remaining .html links...")
    print("=" * 50)
    
    issues = check_html_links()
    
    if not issues:
        print("✅ No .html links found! All URLs appear to be clean.")
    else:
        print(f"❌ Found issues in {len(issues)} files:")
        print()
        
        for filename, file_issues in issues.items():
            print(f"📄 {filename}:")
            for issue in file_issues:
                print(f"   • {issue}")
            print()
    
    print("=" * 50)
    print("🎯 Summary:")
    if issues:
        total_files = len(issues)
        print(f"   • {total_files} files need URL updates")
        print("   • Run manual find/replace to fix remaining links")
    else:
        print("   • All files are ready for clean URL deployment!")

if __name__ == "__main__":
    main()
