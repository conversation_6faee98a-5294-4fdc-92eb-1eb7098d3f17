#!/usr/bin/env python3
"""
SEO Validator for Better Canvas Website
Validates sitemap.xml and robots.txt configuration
"""

import os
import xml.etree.ElementTree as ET
from urllib.parse import urlparse
import re

def validate_sitemap():
    """Validate sitemap.xml structure and content"""
    print("🗺️  Validating sitemap.xml...")
    
    if not os.path.exists('sitemap.xml'):
        print("❌ sitemap.xml not found!")
        return False
    
    try:
        tree = ET.parse('sitemap.xml')
        root = tree.getroot()
        
        # Check namespace
        expected_ns = "http://www.sitemaps.org/schemas/sitemap/0.9"
        if expected_ns not in root.tag:
            print(f"❌ Invalid namespace. Expected: {expected_ns}")
            return False
        
        # Count URLs
        urls = root.findall('.//{http://www.sitemaps.org/schemas/sitemap/0.9}url')
        print(f"✅ Found {len(urls)} URLs in sitemap")
        
        # Validate each URL
        domain_count = {}
        for url in urls:
            loc = url.find('{http://www.sitemaps.org/schemas/sitemap/0.9}loc')
            if loc is not None:
                parsed = urlparse(loc.text)
                domain = parsed.netloc
                domain_count[domain] = domain_count.get(domain, 0) + 1
                
                # Check if URL uses clean format
                if '.html' in parsed.path:
                    print(f"⚠️  URL still contains .html: {loc.text}")
        
        # Check domain consistency
        if len(domain_count) > 1:
            print(f"⚠️  Multiple domains found: {list(domain_count.keys())}")
        else:
            main_domain = list(domain_count.keys())[0] if domain_count else "None"
            print(f"✅ All URLs use domain: {main_domain}")
        
        # Check for required pages
        required_pages = ['/download', '/features', '/themes', '/tutorials', '/faq']
        sitemap_content = open('sitemap.xml').read()
        
        for page in required_pages:
            if f'better-canvas.com{page}' in sitemap_content:
                print(f"✅ Required page found: {page}")
            else:
                print(f"❌ Required page missing: {page}")
        
        return True
        
    except ET.ParseError as e:
        print(f"❌ XML parsing error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error validating sitemap: {e}")
        return False

def validate_robots():
    """Validate robots.txt configuration"""
    print("\n🤖 Validating robots.txt...")
    
    if not os.path.exists('robots.txt'):
        print("❌ robots.txt not found!")
        return False
    
    try:
        with open('robots.txt', 'r') as f:
            content = f.read()
        
        # Check for sitemap reference
        if 'Sitemap:' in content:
            sitemap_match = re.search(r'Sitemap:\s*(.+)', content)
            if sitemap_match:
                sitemap_url = sitemap_match.group(1).strip()
                print(f"✅ Sitemap URL found: {sitemap_url}")
                
                if 'better-canvas.com' in sitemap_url:
                    print("✅ Sitemap URL uses correct domain")
                else:
                    print("⚠️  Sitemap URL domain should be better-canvas.com")
            else:
                print("❌ Sitemap URL not properly formatted")
        else:
            print("❌ No sitemap reference found in robots.txt")
        
        # Check for User-agent directives
        if 'User-agent:' in content:
            print("✅ User-agent directives found")
        else:
            print("❌ No User-agent directives found")
        
        # Check for important Allow directives
        important_paths = ['/download', '/features', '/themes', '/tutorials']
        for path in important_paths:
            if f'Allow: {path}' in content:
                print(f"✅ Explicit allow found: {path}")
            else:
                print(f"ℹ️  No explicit allow for: {path} (general Allow: / should cover this)")
        
        # Check for security blocks
        security_blocks = ['Disallow: /admin/', 'Disallow: /.git/', 'Disallow: /.env']
        for block in security_blocks:
            if block in content:
                print(f"✅ Security block found: {block}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error validating robots.txt: {e}")
        return False

def check_clean_urls():
    """Check if HTML files still contain .html links"""
    print("\n🔗 Checking for remaining .html links in HTML files...")
    
    html_files = [f for f in os.listdir('.') if f.endswith('.html')]
    issues_found = 0
    
    for html_file in html_files:
        try:
            with open(html_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Look for href="*.html" patterns
            html_links = re.findall(r'href="[^"]*\.html"', content)
            if html_links:
                print(f"⚠️  {html_file} contains {len(html_links)} .html links")
                issues_found += len(html_links)
                # Show first few examples
                for link in html_links[:3]:
                    print(f"    Example: {link}")
                if len(html_links) > 3:
                    print(f"    ... and {len(html_links) - 3} more")
        
        except Exception as e:
            print(f"❌ Error checking {html_file}: {e}")
    
    if issues_found == 0:
        print("✅ No .html links found in HTML files!")
    else:
        print(f"⚠️  Total .html links found: {issues_found}")
    
    return issues_found == 0

def main():
    """Main validation function"""
    print("🔍 Better Canvas SEO Validator")
    print("=" * 50)
    
    sitemap_valid = validate_sitemap()
    robots_valid = validate_robots()
    clean_urls = check_clean_urls()
    
    print("\n" + "=" * 50)
    print("📊 Validation Summary:")
    print(f"   Sitemap.xml: {'✅ Valid' if sitemap_valid else '❌ Issues found'}")
    print(f"   Robots.txt:  {'✅ Valid' if robots_valid else '❌ Issues found'}")
    print(f"   Clean URLs:  {'✅ Clean' if clean_urls else '⚠️  Issues found'}")
    
    if sitemap_valid and robots_valid and clean_urls:
        print("\n🎉 All SEO configurations are ready for deployment!")
    else:
        print("\n⚠️  Some issues need attention before deployment.")
    
    print("\n💡 Next steps:")
    print("   1. Submit sitemap to Google Search Console")
    print("   2. Submit sitemap to Bing Webmaster Tools")
    print("   3. Test robots.txt with search engine tools")
    print("   4. Monitor crawl errors and indexing status")

if __name__ == "__main__":
    main()
