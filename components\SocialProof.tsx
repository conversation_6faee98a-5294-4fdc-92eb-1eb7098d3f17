export default function SocialProof() {
  const stats = [
    { number: '100,000+', label: 'Active Users' },
    { number: '4.8/5', label: 'User Rating' },
    { number: '50+', label: 'Available Themes' },
    { number: '2', label: 'Supported Browsers' },
  ]

  const testimonials = [
    {
      text: "<PERSON><PERSON><PERSON><PERSON> made my late-night studying so much easier with dark mode!",
      author: "<PERSON>",
      role: "College Student"
    },
    {
      text: "The GPA calculator is a game-changer. I always know where I stand.",
      author: "<PERSON>",
      role: "University Student"
    },
    {
      text: "Love the custom themes! My <PERSON>vas looks amazing now.",
      author: "<PERSON>",
      role: "Graduate Student"
    }
  ]

  return (
    <section className="py-12 bg-gray-50">
      <div className="container-custom">
        {/* Stats */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {stats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="text-3xl lg:text-4xl font-bold text-primary-600 mb-2">
                {stat.number}
              </div>
              <div className="text-gray-600 font-medium">
                {stat.label}
              </div>
            </div>
          ))}
        </div>

        {/* Testimonials */}
        <div className="grid md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <div key={index} className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
              <div className="flex text-yellow-400 mb-4">
                {[...Array(5)].map((_, i) => (
                  <svg key={i} className="w-5 h-5 fill-current" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
              </div>
              <p className="text-gray-700 mb-4 italic">
                "{testimonial.text}"
              </p>
              <div className="text-sm">
                <div className="font-semibold text-gray-900">{testimonial.author}</div>
                <div className="text-gray-500">{testimonial.role}</div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
