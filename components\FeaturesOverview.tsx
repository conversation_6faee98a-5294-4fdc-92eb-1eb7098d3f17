import Link from 'next/link'

export default function FeaturesOverview() {
  const features = [
    {
      icon: '🌙',
      title: 'Beautiful Dark Mode',
      description: 'Easy on the eyes with fully customizable dark themes. Perfect for late-night studying sessions.',
      link: '/features#dark-mode'
    },
    {
      icon: '🎨',
      title: '50+ Custom Themes',
      description: 'Personalize your Canvas with beautiful themes created by the community.',
      link: '/themes'
    },
    {
      icon: '📊',
      title: 'Smart GPA Calculator',
      description: 'Track your grades and calculate what-if scenarios to stay on top of your academic goals.',
      link: '/features#gpa-calculator'
    },
    {
      icon: '✅',
      title: 'Enhanced Todo List',
      description: 'Never miss an assignment with our improved todo list and assignment tracking.',
      link: '/features#todo-list'
    },
    {
      icon: '🎯',
      title: 'Card Customization',
      description: 'Customize your course cards with colors, images, and layouts that match your style.',
      link: '/features#card-customization'
    },
    {
      icon: '🔔',
      title: 'Smart Reminders',
      description: 'Get browser-wide popup reminders for upcoming assignments and deadlines.',
      link: '/features#reminders'
    }
  ]

  return (
    <section className="section-padding bg-white">
      <div className="container-custom">
        <div className="text-center mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            Powerful Features for Better Learning
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            BetterCanvas transforms your Canvas experience with tools designed to boost productivity and make studying more enjoyable.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div key={index} className="feature-card group">
              <div className="text-4xl mb-4">{feature.icon}</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                {feature.title}
              </h3>
              <p className="text-gray-600 mb-4 leading-relaxed">
                {feature.description}
              </p>
              <Link 
                href={feature.link}
                className="text-primary-600 hover:text-primary-700 font-medium inline-flex items-center group-hover:translate-x-1 transition-transform duration-200"
              >
                Learn More
                <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </Link>
            </div>
          ))}
        </div>

        {/* CTA */}
        <div className="text-center mt-12">
          <Link href="/features" className="btn-primary text-lg px-8 py-4">
            View All Features
          </Link>
        </div>
      </div>
    </section>
  )
}
