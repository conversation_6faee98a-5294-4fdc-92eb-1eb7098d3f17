# Better Canvas Resources

A third-party resource website for the Better Canvas browser extension, featuring downloads, tutorials, themes, and community support.

## About Better Canvas

Better Canvas is a browser extension that enhances Canvas LMS with:
- Dark mode and custom themes
- GPA calculator
- Enhanced todo list
- Card customization
- Assignment reminders
- And much more!

## This Website

This is an unofficial third-party resource site that provides:
- Easy access to download links
- Comprehensive tutorials
- Theme gallery
- FAQ and troubleshooting guides
- Latest updates and changelog

## Tech Stack

- **Framework**: Static HTML pages
- **Styling**: Tailwind CSS (CDN)
- **Deployment**: Any static hosting service (Cloudflare, Netlify, Vercel, GitHub Pages)
- **Language**: HTML, CSS, JavaScript

## Getting Started

### Prerequisites
- Any modern web browser
- Web server (for local development)

### Local Development

1. Clone the repository:
```bash
git clone <repository-url>
cd bettercanvas-hub
```

2. Serve the files locally:
```bash
# Using Python
python -m http.server 8000

# Using Node.js
npx serve .

# Or simply open index.html in your browser
```

3. Open [http://localhost:8000](http://localhost:8000) in your browser.

### Deployment

Simply upload all HTML files to any static hosting service:
- Cloudflare Pages
- Netlify
- Vercel
- GitHub Pages
- Any web server

## Project Structure

```
├── index.html             # Homepage
├── download.html          # Download page
├── features.html          # Features overview
├── themes.html            # Theme gallery
├── tutorials.html         # Tutorials and guides
├── faq.html              # FAQ page
├── contact.html          # Contact/support page
├── troubleshooting.html  # Troubleshooting guide
├── changelog.html        # Updates and changelog
├── privacy.html          # Privacy policy
├── terms.html            # Terms of use
├── disclaimer.html       # Legal disclaimer
├── sitemap.xml           # XML sitemap
├── sitemap.html          # HTML sitemap
├── robots.txt            # Search engine directives
└── README.md             # This file
```

## SEO Features

- Optimized meta tags and Open Graph
- Structured data (JSON-LD)
- Semantic HTML structure
- Fast loading with static generation
- Mobile-responsive design

## Legal Notice

This is an unofficial third-party website. BetterCanvas is developed by the original creators. This site is for informational purposes and provides easy access to official download links and community resources.

## Contributing

Contributions are welcome! Please feel free to submit issues or pull requests.

## License

This website code is open source. Please respect the original BetterCanvas project's licensing terms.
